using Autodesk.Revit.DB;
using Autodesk.Revit.DB.Electrical;
using Autodesk.Revit.UI;
using BecaTransactionsNamesManager;
using Common.Extensions;
using Common.UI.Forms;
using Common.UI.Utilities;
using MEP.SheetCreator.CoreLogic;
using MEP.SheetCreator.CoreLogic.CombinationFormLogic.Batch2DView;
using MEP.SheetCreator.CoreLogic.CombinationFormLogic.DependentViews;
using MEP.SheetCreator.CoreLogic.CombinationFormLogic.ViewSheetPlacer;
using MEP.SheetCreator.UI.UIData;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using View = Autodesk.Revit.DB.View;
using System.Runtime.InteropServices;
using BecaRevitUtilities.Collectors;
using MEP.SheetCreator.CoreLogic.CombinationFormLogic.SheetCreator;
using ParameterValue = MEP.SheetCreator.CoreLogic.CombinationFormLogic.SheetCreator.ParameterValue;
using System.IO;
using System.Diagnostics;
using MEP.SheetCreator.CoreLogic.CombinationFormLogic.VSC_2;
using TaskDialog = Autodesk.Revit.UI.TaskDialog;
using BecaRevitUtilities.Extensions;

namespace MEP.SheetCreator.UI.Forms
{
    public enum SheetCreatorColumnName
    {
        SheetNumber,
        SheetName,
        Parameter,
        BecaDrawn
    }

    public partial class FrmSheetAndViewCreator : BecaBaseForm
    {

        #region Fields
        Autodesk.Revit.ApplicationServices.Application _app;
        Document _doc;
        List<string> _commonParameter;
        string _searchbarPlaceholderText = "Filter parameter name here ...";
        List<FamilySymbol> _titleblocks;

        #region Batch2DView Fields
        Batch2DViewData _batch2DViewData;

        public bool rqChkCreateWS = true;

        public IList<Level> subLevels = new List<Level>();
        public IList<Level> subLevelsSel = new List<Level>();

        public IList<Element> subFloorTemps = new List<Element>();
        public IList<Element> subFloorTempsSel = new List<Element>();

        public IList<Element> subRcpTemps = new List<Element>();
        public IList<Element> subRcpTempsSel = new List<Element>();

        public IList<Element> subScopeBox = new List<Element>();
        public ElementId subScopeBoxSelId = null;

        public string subStrViewPrefix = "";
        public string subStrViewSuffix = "";

        public ViewFamilyType SelectedViewFamily;

        public List<ViewPlan> CreatedViewPlans;

        ViewSheet _viewSheetPreview;
        PreviewControl _previewControl;
        #endregion

        #region DependentViews Fields
        public IEnumerable<ViewPlan> SubViewPlansSel { get => rqListBoxPViews.SelectedItems.Cast<ViewPlan>(); }
        List<Element> _scopeboxes;
        #endregion

        #region SheetCreator
        List<string> _defaultParameterNames = new List<string>();
        List<SheetCreatorItem> _successfullyCreatedSheetItems;

        #endregion

        #region ViewSheetPlacer Fields
        ViewSheetPlacerData _viewSheetPlacerData;

        IList<Autodesk.Revit.DB.View> _allViews;
        List<ListViewItem> _sheetItems;

        public IList<Autodesk.Revit.DB.View> SelectedViewType { get; set; }
        public FilterRule SelectedFilterRule { get; set; }
        public FilterRule SelectedFilterRule2 { get; set; }

        List<ElementId> _placedViewIds = new List<ElementId>();

        public List<ElementId> CreatedDependentViewIds = new List<ElementId>();
        #endregion

        #region Advanced
        public List<VSC_2_CreationItems> ViewSheetCreationItems;
        public bool CreateParent;
        public string OverallName;
        public string ParentName;

        VSC_Data _advancedData;
        DataTable _advancedDataTable;
        DataTable _viewTemplateDT;
        string _overallScale;
        string _dependantScale;
        List<VSC_DrawingType> _selectedDrawingTypes;

        public VSC_TitleblockParameters TitleblockParameters;

        Dictionary<string, string> _customNamingParameters = new Dictionary<string, string>();
        Dictionary<string, string> _namingExamples = new Dictionary<string, string>();
        string _keyOverallScale = "<OverallScale>";
        string _keyDependantScale = "<DependantScale>";
        string _keyLevelInputNumber = "<LevelInputNumber>";
        string _keyLevelInputString = "<LevelInputString>";
        string _keyViewName = "<LevelViewName>";
        string _keyDisciplineCode = "<DisciplineCode>";
        string _keyDisciplineNumber = "<DiciplineNumber>";
        string _keyDrawingType = "<DrawingType>";
        string _keyScopeBoxInputNumber = "<ScopeBoxInputNumber>";
        string _keyOverallScopeBoxInputString = "<OverallScopeBoxInputString>"; // Scopebox Input Strings is getting from the same code
        string _keyParentScopeBoxInputString = "<ParentScopeBoxInputString>";
        string _keyDependantScopeBoxInputString = "<DependantScopeBoxInputString>";

        string _defaultSheetNumber = string.Empty;
        string _defaultSheetName = string.Empty;
        string _defaultOverallViewName = string.Empty;
        string _defaultParentViewName = string.Empty;
        string _defaultDependantViewName = string.Empty;
        #endregion

        #endregion

        #region Constructors

        public FrmSheetAndViewCreator(Autodesk.Revit.ApplicationServices.Application app, Document doc)
        {
            InitializeComponent();

            _app = app;
            _doc = doc;
            _scopeboxes = new FilteredElementCollector(_doc).OfCategory(BuiltInCategory.OST_VolumeOfInterest).ToList();

            LoadBatch2DView();
            LoadDependentView();
            LoadViewSheetPlacer();
            LoadViewSheetPlacerTarget();

            LoadCommonParameter();
            PopulateTitleblockList(doc);
            LoadDefaultColumns();

            var vsc2 = new VSC_2(_app, _doc, this, _advancedDataTable);
            _advancedData = vsc2.Data;
            _advancedDataTable = vsc2.DataTable;
            ViewSheetCreationItems = new List<VSC_2_CreationItems>();
            OverallName = "OVERALL";
            ParentName = "PARENT";

            SetInitialCustomNamingDictionary();
            SetInitialCustomNamingExampleDictionary();
            SetDefaultNamings();
        }

        #endregion

        #region TabControl Methods
        /// <summary>
        /// This Method will show the text for each tabs that doesn't show as a default 
        /// when using vertical tab
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void tc_SCTabs_DrawItem(object sender, DrawItemEventArgs e)
        {
            Graphics g = e.Graphics;
            Brush _textBrush;

            // Get the item from the collection.
            TabPage _tabPage = tc_SCTabs.TabPages[e.Index];

            // Get the real bounds for the tab rectangle.
            System.Drawing.Rectangle _tabBounds = tc_SCTabs.GetTabRect(e.Index);

            if (e.State == DrawItemState.Selected)
            {

                // Draw a different background color, and don't paint a focus rectangle.
                _textBrush = new SolidBrush(System.Drawing.Color.FromArgb(141, 14, 132));
                g.FillRectangle(Brushes.White, e.Bounds);
            }
            else
            {
                _textBrush = new System.Drawing.SolidBrush(e.ForeColor);
                e.DrawBackground();
            }

            // Use our own font.
            System.Drawing.Font _tabFont = new System.Drawing.Font("Arial", 11.0f, FontStyle.Regular, GraphicsUnit.Pixel);

            // Draw string. Center the text.
            StringFormat _stringFlags = new StringFormat();
            _stringFlags.Alignment = StringAlignment.Center;
            _stringFlags.LineAlignment = StringAlignment.Center;
            g.DrawString(_tabPage.Text, _tabFont, _textBrush, _tabBounds, new StringFormat(_stringFlags));
        }

        private void tc_SCTabs_Selected(object sender, TabControlEventArgs e)
        {
            if (e.TabPage.Name == tp_Advanced.Name)
            {
                //this.WindowState = FormWindowState.Minimized;
                //if (VSC_AdvancedFormCoreLogic.RunLogic(_app, _doc))
                //{
                //    this.DialogResult = DialogResult.OK;
                //}
                //else
                //{
                //    this.WindowState = FormWindowState.Normal;
                //    this.BringToFront();
                //}

            }
            else if (e.TabPage.Name == tp_Batch2DViews.Name)
            {
                LoadBatch2DView();
            }
            else if (e.TabPage.Name == tp_DependentViews.Name)
            {
                LoadDependentView();
            }
            else if (e.TabPage.Name == tp_ViewToSheetPlacer.Name)
            {
                LoadViewSheetPlacer();
            }
        }
        #endregion

        #region Batch2DView Methods
        private void LoadBatch2DView()
        {
            _batch2DViewData = new Batch2DViewData(_doc);
            rqListBoxLevels.DataSource = _batch2DViewData.Levels.Select(x => x.Name).ToList();
            rqListFloorViewTemplates.DataSource = _batch2DViewData.FloorViewTemplates.Select(x => x.Name).ToList();
            rqListRcpViewTemplates.DataSource = _batch2DViewData.RCPViewTemplates.Select(x => x.Name).ToList();
            rqComboScopeBox.DataSource = _batch2DViewData.Scopeboxes.Select(x => x.Name).ToList();

        }

        private void btn_CreateViews_Click(object sender, EventArgs e)
        {
            if (rb_FloorPlans.Checked)
                SelectedViewFamily = new FilteredElementCollector(_doc).OfClass(typeof(ViewFamilyType))
                .Cast<ViewFamilyType>().FirstOrDefault<ViewFamilyType>(x => ViewFamily.FloorPlan == x.ViewFamily);
            if (rb_StructuralPlans.Checked)
                SelectedViewFamily = new FilteredElementCollector(_doc).OfClass(typeof(ViewFamilyType))
                .Cast<ViewFamilyType>().FirstOrDefault<ViewFamilyType>(x => ViewFamily.StructuralPlan == x.ViewFamily);
            if (rb_AreaPlans.Checked)
                SelectedViewFamily = new FilteredElementCollector(_doc).OfClass(typeof(ViewFamilyType))
                .Cast<ViewFamilyType>().FirstOrDefault<ViewFamilyType>(x => ViewFamily.AreaPlan == x.ViewFamily);

            if (SelectedViewFamily != null)
            {
                subStrViewPrefix = rqViewPrefix.Text + " ";
                subStrViewSuffix = " " + rqViewSuffix.Text;

                System.Windows.Forms.ListBox.SelectedIndexCollection rqIndicesLv = rqListBoxLevels.SelectedIndices;
                foreach (int rqIndex in rqIndicesLv)
                {
                    subLevelsSel.Add(_batch2DViewData.Levels.ElementAt(rqIndex));
                }

                System.Windows.Forms.ListBox.SelectedIndexCollection rqIndicesFT = rqListFloorViewTemplates.SelectedIndices;
                foreach (int rqIndex in rqIndicesFT)
                {
                    subFloorTempsSel.Add(_batch2DViewData.FloorViewTemplates.ElementAt(rqIndex));
                }

                System.Windows.Forms.ListBox.SelectedIndexCollection rqIndicesRT = rqListRcpViewTemplates.SelectedIndices;
                foreach (int rqIndex in rqIndicesRT)
                {
                    subRcpTempsSel.Add(_batch2DViewData.RCPViewTemplates.ElementAt(rqIndex));
                }

                subScopeBoxSelId = _batch2DViewData.Scopeboxes.Find(x => x.Name == rqComboScopeBox.SelectedItem.ToString())?.Id;

                if (TextBoxUtility.ContainsInvalidCharacter(rqViewPrefix.Text) || TextBoxUtility.ContainsInvalidCharacter(rqViewSuffix.Text))
                {
                    MessageBox.Show("Invalid character found in Prefix or Suffix\nCannot contain any of the following characters:\n" + @"[\/?:&*""><|#%]");
                    DialogResult = DialogResult.None;
                }
            }
            else
            {
                MessageBox.Show("Cannot find selected View Type", "Null View Type");
                this.DialogResult = DialogResult.None;
            }

            int intViewCreated = 0;
            List<ViewPlan> createdViewPlans;

            if (Batch2DViewLogic.RunLogic(_doc, subLevelsSel, subFloorTempsSel, subRcpTempsSel, subScopeBoxSelId, SelectedViewFamily, subStrViewPrefix, subStrViewSuffix, out intViewCreated, out createdViewPlans))
            {
                TaskDialog.Show("Batch 2D View Creation", "Views Created: " + intViewCreated.ToString());
                CreatedViewPlans = createdViewPlans;
                tc_SCTabs.SelectTab(1);
            }
            else
            {
                TaskDialog.Show("Batch 2D View Creation", "Views Created: " + intViewCreated.ToString());
            }

        }

        private void rqViewPrefix_TextChanged(object sender, EventArgs e)
        {
            UpdateNamePreviewLabel();
        }

        private void rqViewSuffix_TextChanged(object sender, EventArgs e)
        {
            UpdateNamePreviewLabel();
        }

        private void rqListFloorViewTemplates_SelectedIndexChanged(object sender, EventArgs e)
        {
            UpdateNamePreviewLabel();
        }

        private void rqListRcpViewTemplates_SelectedIndexChanged(object sender, EventArgs e)
        {
            UpdateNamePreviewLabel();
        }

        private void UpdateNamePreviewLabel()
        {
            if (rqListBoxLevels.SelectedItems.Count == 0)
                return;

            if (rqListFloorViewTemplates.SelectedItems.Count > 0 && rqListBoxLevels.SelectedItems.Count > 0)
            {
                rtb_ViewName.Text = "";
                rtb_ViewName.Text = $"{rqViewPrefix.Text} {rqListBoxLevels.SelectedItems[0].ToString()} {rqListFloorViewTemplates.SelectedItems[0].ToString()} {rqViewSuffix.Text}";
            }
            else if (rqListRcpViewTemplates.SelectedItems.Count > 0 && rqListBoxLevels.SelectedItems.Count > 0)
            {
                rtb_ViewName.Text = "";
                rtb_ViewName.Text = $"{rqViewPrefix.Text} {rqListBoxLevels.SelectedItems[0].ToString()} {rqListRcpViewTemplates.SelectedItems[0].ToString()} {rqViewSuffix.Text}";
            }
        }

        private void btn_FrmParameterPrefix_Click(object sender, EventArgs e)
        {
            using (var frmParamtersPrefix = new FrmParameterList(new FilteredElementCollector(_doc).OfClass(typeof(ViewPlan)).FirstOrDefault().GetOrderedParameters()))
            {
                frmParamtersPrefix.ShowDialog();
                if (frmParamtersPrefix.DialogResult == DialogResult.OK)
                {
                    rqViewPrefix.Text = frmParamtersPrefix.ParametersPrefix;
                }
            }
        }
        #endregion

        #region DependentViews Methods
        private void LoadDependentView()
        {
            if (chb_ShowAllViews.Checked)
            {
                ucViewNameFiltering.Enabled = true;

                rqListScopeBox.DataSource = _scopeboxes;
                rqListScopeBox.DisplayMember = "Name";

                // Getting All The Views
                var views = new FilteredElementCollector(_doc).OfClass(typeof(ViewPlan)).Cast<ViewPlan>().Where(x => !x.IsTemplate && ((Autodesk.Revit.DB.ViewType.FloorPlan == x.ViewType) || (Autodesk.Revit.DB.ViewType.CeilingPlan == x.ViewType) || (Autodesk.Revit.DB.ViewType.EngineeringPlan == x.ViewType))).ToList();

                PopulateViewListBox(views);

                ucViewNameFiltering.Intialize(_doc);
                ucViewNameFiltering.PostViewFiltering += UcViewNameFiltering_PostViewFiltering;
            }
            else
            {
                ucViewNameFiltering.Enabled = false;
                rqListBoxPViews.DataSource = null;

                rqListScopeBox.DataSource = _scopeboxes;
                rqListScopeBox.DisplayMember = "Name";

                if (CreatedViewPlans != null && CreatedViewPlans.Count > 0)
                {
                    PopulateViewListBox(CreatedViewPlans);
                }
            }
            
        }

        private void PopulateViewListBox(List<ViewPlan> views)
        {
            var viewSorted = from ViewPlan rqv in views orderby rqv.ViewType, rqv.Name ascending select rqv;

            IList<ViewPlan> viewPlans = new List<ViewPlan>();
            foreach (ViewPlan rqView in viewSorted)
            {
                if (rqView.GetPrimaryViewId().IntegerValue() == -1)
                {
                    viewPlans.Add(rqView);
                }
            }

            rqListBoxPViews.DataSource = viewPlans;
            rqListBoxPViews.DisplayMember = "Name";
        }

        private void UcViewNameFiltering_PostViewFiltering(object sender, object e)
        {
            var views = (e as Element[]).Cast<Autodesk.Revit.DB.View>();

            // Getting All The Views
            IList<ViewPlan> rqViewsRD = new List<ViewPlan>(views
               .Where(v => v is ViewPlan && !v.IsTemplate && ((Autodesk.Revit.DB.ViewType.FloorPlan == v.ViewType) || 
               (Autodesk.Revit.DB.ViewType.CeilingPlan == v.ViewType) || (Autodesk.Revit.DB.ViewType.EngineeringPlan == v.ViewType)
               || Autodesk.Revit.DB.ViewType.DraftingView == v.ViewType))
               .Cast<ViewPlan>());

            IOrderedEnumerable<ViewPlan> rqViewsSorted = from ViewPlan rqv in rqViewsRD orderby rqv.ViewType, rqv.Name ascending select rqv;

            IList<ViewPlan> rqPViews = new List<ViewPlan>();
            foreach (ViewPlan rqView in rqViewsSorted)
            {
                if (rqView.GetPrimaryViewId().IntegerValue() == -1)
                {
                    rqPViews.Add(rqView);
                }
            }

            rqListBoxPViews.DataSource = rqPViews;
            rqListBoxPViews.DisplayMember = "Name";
        }

        private void btn_SelectAllViews_Click(object sender, EventArgs e)
        {
            ListBoxUtility.SelectALL(rqListBoxPViews);
        }

        private void btn_SelectnonViews_Click(object sender, EventArgs e)
        {
            ListBoxUtility.SelectNone(rqListBoxPViews);
        }

        private void btn_SelectInverseViews_Click(object sender, EventArgs e)
        {
            ListBoxUtility.SelectInverse(rqListBoxPViews);
        }

        private void btn_StartDependentViews_Click(object sender, EventArgs e)
        {
            int intDepViewsCreated = 0;
            List<ElementId> createdDependentViewIds;
            
            var selectedScopeboxes = new List<Element>();
            foreach (var item in rqListScopeBox.SelectedItems)
            {
                foreach (var sb in _scopeboxes)
                {
                    if (sb.Name == (item as Element).Name)
                        selectedScopeboxes.Add(sb);
                }
            }

            if (DependentViewsLogic.RunLogic(_doc, SubViewPlansSel, selectedScopeboxes, out intDepViewsCreated, out createdDependentViewIds))
            {
                TaskDialog.Show("Beca MEP Tools", "Dependant Views Created: " + intDepViewsCreated);
                if (CreatedDependentViewIds.Count == 0)
                    CreatedDependentViewIds = createdDependentViewIds;
                else
                {
                    foreach (var item in createdDependentViewIds)
                    {
                        CreatedDependentViewIds.Add(item);
                    }
                } 
            }
            else
            {
                TaskDialog.Show("Beca MEP Tools", "Dependant Views Created: " + intDepViewsCreated);
            }
        }
        #endregion

        #region Sheet Creator
        private void LoadCommonParameter()
        {
            _commonParameter = GetCommonParameterListFromCategories(_doc, BuiltInCategory.OST_Sheets);
            lb_Parameter.DataSource = _commonParameter;
        }

        private void PopulateTitleblockList(Document doc)
        {
            _titleblocks = ElementCollectorUtility.GetAllTitleBlocks(doc).Select(x => x as FamilySymbol).ToList();
            lb_TitleBlocks.DataSource = _titleblocks.Select(x => x.Name).ToList();

            //var stamps = _titleblocks[1].GetSubelements();//.Where(x => x.Category.Name == "Generic Annotations").Select(e => e.Element.Name).ToList();
            //List<Element> listFamilyInstances = new FilteredElementCollector(doc, doc.ActiveView.Id)
            //.OfClass(typeof(FamilyInstance))
            //.Cast<FamilyInstance>()
            //.Where(a => a.SuperComponent == null)
            //.SelectMany(a => a.GetSubComponentIds())
            //.Select(a => doc.GetElement(a))
            //.ToList();

            //var stamps = new FilteredElementCollector(doc).OfCategory(BuiltInCategory.OST_TitleBlocks).WhereElementIsNotElementType().ToElements().ToList().Find(x => x.Id.IntegerValue == 490515)?.Parameters;

            //var stamps = new FilteredElementCollector(doc)
            //.OfClass(typeof(FamilyInstance))
            //.OfCategory(BuiltInCategory.OST_TitleBlocks)
            //.Cast<FamilyInstance>()
            //.Where(a => a.SuperComponent != null)
            //.SelectMany(a => a.GetSubComponentIds())
            //.Select(a => doc.GetElement(a))
            //.ToList()[0].Parameters;

            //foreach (Parameter item in stamps/*.OrderBy(x => xName)*/)
            //{
            //    lb_Stamp1.Items.Add(item.Definition.Name);
            //}
            //lb_Stamp1.DataSource = stamps;

            //var stamps = new FilteredElementCollector(doc).OfCategory(BuiltInCategory.OST_GenericAnnotation).WhereElementIsNotElementType().ToElements().Select(x => x.Name).ToList();
            //lb_Stamp1.DataSource = stamps;
        }

        private void LoadDefaultColumns()
        {
            _defaultParameterNames.Add("Sheet Number");
            _defaultParameterNames.Add("Sheet Name");
            _defaultParameterNames.Add("Beca Drawn");
            _defaultParameterNames.Add("Beca Drawn Date");
            _defaultParameterNames.Add("Beca Designed");
            _defaultParameterNames.Add("Beca Design Date");
            _defaultParameterNames.Add("Beca Revision");

            foreach (var selectedParameterName in _defaultParameterNames)
            {
                AddColumnToDataGrid(selectedParameterName);
            }
        }

        private void AddColumnToDataGrid(string selectedParameterName)
        {
            if (adgv_FromExcel.Columns[selectedParameterName] == null && selectedParameterName != null)
            {
                DataGridViewColumn col = new DataGridViewTextBoxColumn();
                col.HeaderText = selectedParameterName;
                col.Name = selectedParameterName;
                adgv_FromExcel.Columns.Add(col);
            }
        }

        private void LoadSheetItemsFromExcel()
        {
            adgv_FromExcel.Columns.Clear();

            OpenFileDialog openFileDialog1 = new OpenFileDialog();
            DialogResult result = openFileDialog1.ShowDialog(); // Show the dialog.
            if (result == DialogResult.OK) // Check if Result == "OK".
            {
                adgv_FromExcel.DataSource = ConvertCSVtoDataTable(openFileDialog1.FileName, ','); //assign DataTable as Datasource for DataGridview

            }
        }

        public static DataTable ConvertCSVtoDataTable(string filePath, char delimiter)
        {
            DataTable dataTable = new DataTable();

            try
            {
                using (StreamReader sr = new StreamReader(filePath))
                {
                    string[] headers = sr.ReadLine().Split(delimiter);

                    foreach (string header in headers)
                    {
                        dataTable.Columns.Add(header);
                    }

                    while (!sr.EndOfStream)
                    {
                        string[] rows = sr.ReadLine().Split(delimiter);

                        if (rows.Length == headers.Length)
                        {
                            DataRow dataRow = dataTable.NewRow();
                            for (int i = 0; i < headers.Length; i++)
                            {
                                dataRow[i] = rows[i];
                            }
                            dataTable.Rows.Add(dataRow);
                        }
                        else
                        {
                            // Handle error or log a message if the number of columns doesn't match the header
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // Handle exceptions (e.g., file not found, invalid format, etc.)
                MessageBox.Show(ex.Message);
            }

            return dataTable;
        }

        private void btn_ImportExcel_Click(object sender, EventArgs e)
        {
            LoadSheetItemsFromExcel();
        }

        private void btn_ExportToExcel_Click(object sender, EventArgs e)
        {
            var datetime = DateTime.Now.ToString("_dd MMMM yyyy, hhmmsstt");
            var directory = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments), "View Sheet Creator");
            var fileName = directory + @"\SheetCreator_" + datetime + ".csv";

            if (!Directory.Exists(directory))
            {
                try
                {
                    Directory.CreateDirectory(directory);
                }
                catch (IOException ex)
                {
                    MessageBox.Show("It wasn't possible to write the data to the disk." + ex.Message);
                }
            }
            try
            {
                File.WriteAllLines(fileName, ExportSheetCreatorFromUI(), Encoding.UTF8);

                DialogResult dialogResult = MessageBox.Show(
                    "Sheet Creator columns has been exported to\n\n" + directory +
                    "\n\nWould you like to open the file?",
                    "Info",
                    MessageBoxButtons.YesNo);
                if (dialogResult == DialogResult.Yes)
                {
                    Process.Start(fileName);
                }
                else
                    this.DialogResult = DialogResult.None;

            }
            catch (Exception ex)
            {
                MessageBox.Show("Error :" + ex.Message);
            }
        }

        private string[] ExportSheetCreatorFromUI()
        {
            // Header and Columns
            int columnCount = adgv_FromExcel.Columns.Count;
            string columnNames = "";
            string[] outputCsv = new string[adgv_FromExcel.Rows.Count + 1];
            for (int i = 0; i < columnCount; i++)
            {
                if (i == columnCount - 1)
                    columnNames += adgv_FromExcel.Columns[i].HeaderText.ToString();
                else
                    columnNames += adgv_FromExcel.Columns[i].HeaderText.ToString() + ",";
            }
            outputCsv[0] += columnNames;

            return outputCsv;
        }

        private List<string> GetCommonParameterListFromCategories(Document doc, BuiltInCategory category)
        {
            var listOfParameterList = new List<List<string>>();
            var parameterNames = new List<string>();
            foreach (var parameterSet in new FilteredElementCollector(doc).OfCategory(category).Select(x => x.Parameters))
            {
                foreach (Parameter param in parameterSet)
                {
                    if (param.StorageType == StorageType.String)
                    {
                        var name = param.Definition.Name;
                        if (!string.IsNullOrEmpty(name))
                            parameterNames.Add(name);
                    }
                }
            }
            listOfParameterList.Add(parameterNames);

            return listOfParameterList.Aggregate<IEnumerable<string>>((previousList, nextList) => previousList.Intersect(nextList)).Distinct().OrderBy(x => x).ToList();

        }

        // Double click to add column
        private void lb_Parameter_DoubleClick(object sender, EventArgs e)
        {
            var selectedParameterName = lb_Parameter.SelectedItem?.ToString();

            AddColumnToDataGrid(selectedParameterName);
        }

        // Double click to delete column
        private void adgv_FromExcel_ColumnHeaderMouseDoubleClick(object sender, DataGridViewCellMouseEventArgs e)
        {
            // Delete column in Sheet Creator
            DialogResult dr = MessageBox.Show("Do you want to delete this column?",
                      "Delete column", MessageBoxButtons.YesNo);
            switch (dr)
            {
                case DialogResult.Yes:
                    adgv_FromExcel.Columns.Remove(adgv_FromExcel.Columns[e.ColumnIndex].Name);
                    break;
                case DialogResult.No:
                    break;
            }
        }

        private void tb_FilterParameterName_TextChanged(object sender, EventArgs e)
        {
            lb_Parameter.DataSource = null;

            List<string> filteredItems = new List<string>(); 
            if (!string.IsNullOrEmpty(tb_FilterParameterName.Text))
            {
                filteredItems = _commonParameter.Where(i => i.CaseContains(tb_FilterParameterName.Text, StringComparison.CurrentCultureIgnoreCase)).ToList();
                lb_Parameter.DataSource = filteredItems;
            }
            else
            {
                lb_Parameter.DataSource = _commonParameter;
            }
        }

        private void tb_FilterParameterName_Enter(object sender, EventArgs e)
        {
            tb_FilterParameterName.Text = "";
        }

        private void tb_FilterParameterName_Leave(object sender, EventArgs e)
        {
            //tb_FilterParameterName.Text = _searchbarPlaceholderText;
        }

        private void btn_CreateSheets_Click(object sender, EventArgs e)
        {
            var sheetCreatorItems = new List<SheetCreatorItem>();
            var selectedTitleblockId = _titleblocks.Find(x => x.Name == lb_TitleBlocks.SelectedItem.ToString()).Id;
            
            foreach (DataGridViewRow row in adgv_FromExcel.Rows)
            {
                var sheetCreatorItem = new SheetCreatorItem();
                sheetCreatorItem.TitleblockId = selectedTitleblockId;
                for (int i = 0; i < row.Cells.Count; i++)
                {
                    var cellValue = row.Cells[i].Value;
                    var columnName = adgv_FromExcel.Columns[row.Cells[i].ColumnIndex].Name;

                    if (cellValue == null)
                        continue;

                    if (columnName.ToLower().Contains("sheet") && columnName.ToLower().Contains("name"))
                    {
                        sheetCreatorItem.SheetName = cellValue.ToString();
                    }
                    else if (columnName.ToLower().Contains("sheet") && columnName.ToLower().Contains("number"))
                    {
                        sheetCreatorItem.SheetNumber = cellValue.ToString();
                    }
                    else
                    {
                        sheetCreatorItem.ParametersAndValues.Add(new ParameterValue() { Parameter = columnName, Value = cellValue.ToString() });
                    }
                }
                sheetCreatorItems.Add(sheetCreatorItem);
            }

            if (SheetCreatorLogic.RunLogic(_doc, sheetCreatorItems))
            {
                // Set Parameters
                using (var trans = new Transaction(_doc, "Set Sheet parameters"))
                {
                    trans.Start();
                    _successfullyCreatedSheetItems = sheetCreatorItems.Where(x => x.SuccessfullyCreated).ToList();
                    foreach (var item in _successfullyCreatedSheetItems)
                    {
                        foreach (var parameterValue in item.ParametersAndValues)
                        {
                            var parameter = item.CreatedSheet?.LookupParameter(parameterValue.Parameter);
                            if (parameter != null)
                            {
                                parameter.Set(parameterValue.Value);
                            }

                        }
                    }
                    trans.Commit();
                }

                TaskDialog.Show("Beca MEP Tools", $"Sheets created: {_successfullyCreatedSheetItems.Count}\nSheets failed to create: {sheetCreatorItems.Where(x => !x.SuccessfullyCreated).Count()}");
            }
            else
            {
                TaskDialog.Show("Beca MEP Tools", $"Fail to create sheets.");
            }
        }

        #endregion

        #region ViewSheetPlacer Methods
        private void LoadViewSheetPlacer()
        {
            _sheetItems = new List<ListViewItem>();
            _viewSheetPlacerData = new ViewSheetPlacerData(_doc);
            _allViews = _viewSheetPlacerData.Views;
            SelectedViewType = _viewSheetPlacerData.Views;

            //Populate views
            ListAllViews(_allViews);

            //Popuate legends
            string[] strLegendRows = new string[2];
            foreach (Autodesk.Revit.DB.View viewLegend in _viewSheetPlacerData.ViewLegends)
            {
                strLegendRows[0] = viewLegend.Name;
                strLegendRows[1] = viewLegend.Id.ToString();
                ListViewItem rqLVitem = new ListViewItem(strLegendRows);
                rqLegendList.Items.Add(rqLVitem);
            }

            //Populate sheets
            sheetList.Items.Clear();
            string[] strSheetsRows = new string[3];
            foreach (ViewSheet viewSheet in _viewSheetPlacerData.ViewSheets)
            {
                strSheetsRows[0] = viewSheet.SheetNumber;
                strSheetsRows[1] = viewSheet.Name;
                strSheetsRows[2] = viewSheet.Id.ToString();
                ListViewItem rqLVitem = new ListViewItem(strSheetsRows);
                _sheetItems.Add(rqLVitem);

                foreach (var id in viewSheet.GetAllPlacedViews())
                {
                    _placedViewIds.Add(id);
                    var parentViewId = (viewSheet.Document.GetElement(id) as View).GetPrimaryViewId();
                    if (!_placedViewIds.Contains(parentViewId))
                        _placedViewIds.Add(parentViewId);
                }
            }
            sheetList.Items.AddRange(_sheetItems.ToArray());

            //Populate viewTemplate dropdownlist
            IList<ViewType> allViewTypes = _allViews.Select(x => x.ViewType).Distinct().ToList();
            foreach (var item in allViewTypes)
            {
                ddlViewType.Items.Add(item);
            }

            ddlAndOr.SelectedItem = "None";
            ddlViewType.SelectedIndex = 0;
        }

        private void AddSheetToTarget(ListView sheetList)
        {
            List<string[]> rqListSheetSel = new List<string[]>();
            ListView.SelectedIndexCollection rqIndicesSheets = sheetList.SelectedIndices;
            int i = 0;
            foreach (int rqIndex in rqIndicesSheets)
            {
                string[] rqStrOneRow = new string[3];
                rqStrOneRow[0] = sheetList.Items[rqIndex - i].SubItems[0].Text;
                rqStrOneRow[1] = sheetList.Items[rqIndex - i].SubItems[1].Text;
                rqStrOneRow[2] = sheetList.Items[rqIndex - i].SubItems[2].Text;
                rqListSheetSel.Add(rqStrOneRow);
                sheetList.Items[rqIndex - i].Remove();
                i++;
            }

            foreach (string[] strElem in rqListSheetSel)
            {
                string[] strSheetsSelRows = new string[3];
                strSheetsSelRows[0] = strElem[0];
                strSheetsSelRows[1] = strElem[1];
                strSheetsSelRows[2] = strElem[2];
                ListViewItem rqSheetRowSelitem = new ListViewItem(strSheetsSelRows);
                rqTgtSheet.Items.Add(rqSheetRowSelitem);
            }
            ListColors(rqTgtSheet);
        }
        private void AddViewToTarget(ListView viewList)
        {
            List<string[]> rqListViewsSel = new List<string[]>();
            ListView.SelectedIndexCollection rqIndicesViews = viewList.SelectedIndices;
            int i = 0;
            foreach (int rqIndex in rqIndicesViews)
            {
                string[] rqStrOneRow = new string[2];
                rqStrOneRow[0] = viewList.Items[rqIndex - i].SubItems[0].Text;
                rqStrOneRow[1] = viewList.Items[rqIndex - i].SubItems[1].Text;
                rqListViewsSel.Add(rqStrOneRow);
                viewList.Items[rqIndex - i].Remove();
                i++;
            }

            foreach (string[] strElem in rqListViewsSel)
            {
                string[] strSheetsSelRows = new string[3];
                strSheetsSelRows[0] = strElem[0];
                strSheetsSelRows[1] = strElem[1];
                ListViewItem rqSheetRowSelitem = new ListViewItem(strSheetsSelRows);
                rqTgtView.Items.Add(rqSheetRowSelitem);
            }
            ListColors(rqTgtView);
        }
        private void AddLegendToTarget(ListView legendList)
        {
            List<string[]> rqListLegendSel = new List<string[]>();
            ListView.SelectedIndexCollection rqIndicesLegends = legendList.SelectedIndices;
            foreach (int rqIndex in rqIndicesLegends)
            {
                string[] rqStrOneRow = new string[2];
                rqStrOneRow[0] = legendList.Items[rqIndex].SubItems[0].Text;
                rqStrOneRow[1] = legendList.Items[rqIndex].SubItems[1].Text;
                rqListLegendSel.Add(rqStrOneRow);
            }

            foreach (string[] strElem in rqListLegendSel)
            {
                string[] strRows = new string[3];
                strRows[0] = strElem[0];
                strRows[1] = strElem[1];
                ListViewItem rqSheetRowSelitem = new ListViewItem(strRows);
                rqTgtLegend.Items.Add(rqSheetRowSelitem);
            }
            ListColors(rqTgtLegend);
        }

        private void ListColors(ListView list)
        {
            foreach (ListViewItem item in list.Items)
            {
                item.BackColor = item.Index % 2 == 0 ? System.Drawing.Color.White : System.Drawing.Color.LightGray;
            }
        }

        private void ListAllViews(IList<View> allViews)
        {
            rqViewsList.Items.Clear();
            panel1.Enabled = false;
            chb_HidePlacedViews.Enabled = false;

            foreach (View view in allViews)
            {
                if (!chb_NewViews.Checked)
                {
                    if (CreatedDependentViewIds.Contains(view.Id))
                        SetListViewItem(view);
                }
                else
                {
                    panel1.Enabled = true;
                    chb_HidePlacedViews.Enabled = true;

                    SetListViewItem(view);
                }

            }
        }

        private void SetListViewItem(View view)
        {
            string[] strViewRows = new string[2];
            strViewRows[0] = view.Name;
            strViewRows[1] = view.Id.ToString();
            ListViewItem listViewItem = new ListViewItem(strViewRows);
            rqViewsList.Items.Add(listViewItem);
        }

        private void rqSheetAdd_Click(object sender, EventArgs e)
        {
            AddSheetToTarget(sheetList);
        }

        private void rqViewAdd_Click(object sender, EventArgs e)
        {
            AddViewToTarget(rqViewsList);
        }

        private void sheetList_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            AddSheetToTarget(sheetList);
        }

        private void rqViewsList_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            AddViewToTarget(rqViewsList);
        }

        private void btn_LegenAdd_Click(object sender, EventArgs e)
        {
            AddLegendToTarget(rqLegendList);
        }

        private void rqLegendList_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            AddLegendToTarget(rqLegendList);
        }

        private void rqAddToAll_Click(object sender, EventArgs e)
        {
            ListView.SelectedIndexCollection rqIndicesLegends = rqLegendList.SelectedIndices;
            if (rqIndicesLegends.Count == 1)
            {
                int rqIndexSel = rqIndicesLegends[0];
                string[] rqStrOneRow = new string[2];
                rqStrOneRow[0] = rqLegendList.Items[rqIndexSel].SubItems[0].Text;
                rqStrOneRow[1] = rqLegendList.Items[rqIndexSel].SubItems[1].Text;

                foreach (ListViewItem rqItem in rqTgtLegend.Items)
                {
                    if (rqItem.SubItems[0].Text == "")
                    {
                        rqItem.SubItems[0].Text = rqStrOneRow[0];
                        rqItem.SubItems[1].Text = rqStrOneRow[1];
                    }
                }

                int intLegendTotal = rqTgtLegend.Items.Count;
                int intSheetTotal = rqTgtSheet.Items.Count;

                if (intSheetTotal - intLegendTotal > 0)
                {
                    for (int i = 0; i < intSheetTotal - intLegendTotal; i++)
                    {
                        ListViewItem rqNewItem = new ListViewItem(rqStrOneRow);
                        rqTgtLegend.Items.Add(rqNewItem);
                    }
                }
            }
        }

        private void ddlViewType_SelectedIndexChanged(object sender, EventArgs e)
        {
            rqViewsList.Items.Clear();
            IList<View> ShownViewTypes = new List<View>();
            if (ddlViewType.SelectedItem.ToString() == "All")
            {
                ListAllViews(_allViews);
            }
            else
            {
                for (int i = 0; i < _allViews.Count(); i++)
                {
                    if (_allViews[i].ViewType.ToString() == ddlViewType.SelectedItem.ToString())
                    {
                        SetListViewItem(_allViews[i]);
                        ShownViewTypes.Add(_allViews[i]);
                    }
                }
                SelectedViewType = ShownViewTypes;
            }
        }

        private void btnFilter_Click(object sender, EventArgs e)
        {
            rqViewsList.Items.Clear();
            for (int i = 0; i < SelectedViewType.Count(); i++)
            {
                SetFilterRule(ddlFilterRule.SelectedItem?.ToString(), SelectedViewType[i], tbValue1.Text);
                if (ddlAndOr.SelectedItem.ToString() == "And")
                {
                    SetFilterRule2(ddlFilterRule2.SelectedItem?.ToString(), SelectedViewType[i], tbValue2.Text);
                    if (SelectedFilterRule.ElementPasses(SelectedViewType[i]) && SelectedFilterRule2.ElementPasses(SelectedViewType[i]))
                    {
                        SetListViewItem(SelectedViewType[i]);
                    }
                }
                else if (ddlAndOr.SelectedItem?.ToString() == "Or")
                {
                    SetFilterRule2(ddlFilterRule2.SelectedItem?.ToString(), SelectedViewType[i], tbValue2.Text);
                    if (SelectedFilterRule.ElementPasses(SelectedViewType[i]) || SelectedFilterRule2.ElementPasses(SelectedViewType[i]))
                    {
                        SetListViewItem(SelectedViewType[i]);
                    }
                }
                else
                {
                    if (SelectedFilterRule.ElementPasses(SelectedViewType[i]))
                    {
                        SetListViewItem(SelectedViewType[i]);
                    }
                }
            }
        }

        private void SetFilterRule(string text, View view, string inputText)
        {
            switch (text)
            {
                case "":
                    break;
                case "Begins with":
                    SelectedFilterRule = Utilities.ViewNameBeginsWith(view, inputText);
                    break;
                case "Ends with":
                    SelectedFilterRule = Utilities.ViewNameEndsWith(view, inputText);
                    break;
                case "Contains":
                    SelectedFilterRule = Utilities.ViewNameContains(view, inputText);
                    break;
                case "Equals":
                    SelectedFilterRule = Utilities.ViewNameEquals(view, inputText);
                    break;
                case "Not begins with":
                    SelectedFilterRule = Utilities.ViewNameNotBeginsWith(view, inputText);
                    break;
                case "Not ends with":
                    SelectedFilterRule = Utilities.ViewNameNotEndsWith(view, inputText);
                    break;
                case "Not contains":
                    SelectedFilterRule = Utilities.ViewNameNotContains(view, inputText);
                    break;
                case "Not equals":
                    SelectedFilterRule = Utilities.ViewNameNotEquals(view, inputText);
                    break;
            }
        }

        private void SetFilterRule2(string text, View view, string inputText)
        {
            switch (text)
            {
                case "":
                    break;
                case "Begins with":
                    SelectedFilterRule2 = Utilities.ViewNameBeginsWith(view, inputText);
                    break;
                case "Ends with":
                    SelectedFilterRule2 = Utilities.ViewNameEndsWith(view, inputText);
                    break;
                case "Contains":
                    SelectedFilterRule2 = Utilities.ViewNameContains(view, inputText);
                    break;
                case "Equals":
                    SelectedFilterRule2 = Utilities.ViewNameEquals(view, inputText);
                    break;
                case "Not begins with":
                    SelectedFilterRule2 = Utilities.ViewNameNotBeginsWith(view, inputText);
                    break;
                case "Not ends with":
                    SelectedFilterRule2 = Utilities.ViewNameNotEndsWith(view, inputText);
                    break;
                case "Not contains":
                    SelectedFilterRule2 = Utilities.ViewNameNotContains(view, inputText);
                    break;
                case "Not equals":
                    SelectedFilterRule2 = Utilities.ViewNameNotEquals(view, inputText);
                    break;
            }
        }

        private void ddlAndOr_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (ddlAndOr.SelectedItem.ToString() == "None")
            {
                label8.Enabled = false;
                label9.Enabled = false;
                ddlFilterRule2.Enabled = false;
                tbValue2.Enabled = false;
                tbValue2.Text = "";
                ddlFilterRule2.Text = "";
            }
            else
            {
                label8.Enabled = true;
                label9.Enabled = true;
                ddlFilterRule2.Enabled = true;
                tbValue2.Enabled = true;
            }
        }

        private void tbValue1_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == 13)
            {
                if (!tbValue1.AcceptsReturn)
                {
                    btnFilter.PerformClick();
                }
            }
        }

        private void tbValue2_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == 13)
            {
                if (!tbValue1.AcceptsReturn)
                {
                    btnFilter.PerformClick();
                }
            }
        }

        private void txtResetFilter_Click(object sender, EventArgs e)
        {
            sheetList.Items.Clear();
            ListAllViews(_allViews);
            ddlViewType.SelectedIndex = 0;
            ddlAndOr.SelectedItem = "None";
            ddlFilterRule.Text = "";
            tbValue1.Text = "";
        }

        private void txtBox_SheetNameFiltering_TextChanged(object sender, EventArgs e)
        {
            FilterSheetNameAndNumber();
        }

        private void chb_HidePlacedViews_CheckedChanged_1(object sender, EventArgs e)
        {
            rqViewsList.Items.Clear();

            if (chb_HidePlacedViews.Checked)
            {
                foreach (View view in _allViews)
                {
                    if (chb_HidePlacedViews.Checked)
                    {
                        if (!_placedViewIds.Contains(view.Id))
                            SetListViewItem(view);
                    }
                }
            }
            else
            {
                foreach (View view in _allViews)
                {
                    SetListViewItem(view);
                }
            }

        }

        private void chb_NewViews_CheckedChanged(object sender, EventArgs e)
        {
            ListAllViews(_allViews);

        }

        private void txtBox_SheetNumberFiltering_TextChanged(object sender, EventArgs e)
        {
            FilterSheetNameAndNumber();
        }

        private void FilterSheetNameAndNumber()
        {
            sheetList.Items.Clear(); // clear list items before adding 

            IEnumerable<ListViewItem> filteredItems = null;
            if (!string.IsNullOrEmpty(txtBox_SheetNameFiltering.Text) && !string.IsNullOrEmpty(txtBox_SheetNumberFiltering.Text))
            {
                // filter the items match with search key and add result to list view 
                filteredItems = _sheetItems.Where(i => i.SubItems[1].Text.CaseContains(txtBox_SheetNameFiltering.Text, StringComparison.CurrentCultureIgnoreCase) &&
                i.SubItems[0].Text.CaseContains(txtBox_SheetNumberFiltering.Text, StringComparison.CurrentCultureIgnoreCase));
            }
            else if (!string.IsNullOrEmpty(txtBox_SheetNameFiltering.Text) && string.IsNullOrEmpty(txtBox_SheetNumberFiltering.Text))
            {
                filteredItems = _sheetItems.Where(i => i.SubItems[1].Text.CaseContains(txtBox_SheetNameFiltering.Text, StringComparison.CurrentCultureIgnoreCase));
            }
            else if (!string.IsNullOrEmpty(txtBox_SheetNumberFiltering.Text) && string.IsNullOrEmpty(txtBox_SheetNameFiltering.Text))
            {
                filteredItems = _sheetItems.Where(i => i.SubItems[0].Text.CaseContains(txtBox_SheetNumberFiltering.Text, StringComparison.CurrentCultureIgnoreCase));
            }
            else
            {
                filteredItems = _sheetItems;
            }
            sheetList.Items.AddRange(filteredItems.ToArray());
        }

        #endregion

        #region ViewSheetPlacerTarget Methods
        private void LoadViewSheetPlacerTarget()
        {
            cb_PlaceCenter.Checked = true;
            Image rqUpViews = rqViewUp.Image;
            rqUpViews.RotateFlip(RotateFlipType.RotateNoneFlipY);
            Image rqUpLegends = rqLegendUp.Image;
            rqUpLegends.RotateFlip(RotateFlipType.RotateNoneFlipY);

        }

        //*** Function for move down rows in a listview
        private static void rqMoveDownRows(ListView subRqListSheet, ListView subRqListView)
        {
            ListView.SelectedIndexCollection rqIndices = subRqListView.SelectedIndices;

            int intLastSheetItemPos = subRqListSheet.Items.Count - 1;
            int intSelTotal = rqIndices.Count;
            int intLastSelItemPos = rqIndices[intSelTotal - 1];
            int intLastViewItemPos = subRqListView.Items.Count - 1;

            //*** If a sequential selection at the end of the (view/legend) list and view list is shorter than the sheet list
            //*** add a new row below and then move down one row
            if (BlCheckSequen(rqIndices) && intLastSelItemPos == intLastViewItemPos && intLastSheetItemPos - intLastViewItemPos >= 1)
            {
                //go from the last selected row to the top
                for (int i = rqIndices.Count - 1; i >= 0; i--)
                {
                    int rqIndex = rqIndices[i];
                    //** if this is the last row of the selection
                    if (i == rqIndices.Count - 1)
                    {
                        //Add a new row with the value of the last selection
                        string[] strRows = new string[2];
                        strRows[0] = subRqListView.Items[rqIndex].SubItems[0].Text;
                        strRows[1] = subRqListView.Items[rqIndex].SubItems[1].Text;
                        ListViewItem rqRowitem = new ListViewItem(strRows);
                        subRqListView.Items.Add(rqRowitem);

                        //* In case of only one row selected, make the selected row empty
                        if (rqIndices.Count == 1)
                        {
                            subRqListView.Items[rqIndex].SubItems[0].Text = "";
                            subRqListView.Items[rqIndex].SubItems[1].Text = "";
                        }
                        else
                        //* in case of more than one row selected, make the selected row values of the row above.
                        {
                            subRqListView.Items[rqIndex].SubItems[0].Text = subRqListView.Items[rqIndex - 1].SubItems[0].Text;
                            subRqListView.Items[rqIndex].SubItems[1].Text = subRqListView.Items[rqIndex - 1].SubItems[1].Text;
                        }
                    }

                    //** If this is the first row of the selection, just make it blank
                    else if (i == 0)
                    {
                        subRqListView.Items[rqIndex].SubItems[0].Text = "";
                        subRqListView.Items[rqIndex].SubItems[1].Text = "";
                    }
                    else
                    //** as long as it is not the last row or the first row of the selection, make the selected row values of the row above.
                    {
                        subRqListView.Items[rqIndex].SubItems[0].Text = subRqListView.Items[rqIndex - 1].SubItems[0].Text;
                        subRqListView.Items[rqIndex].SubItems[1].Text = subRqListView.Items[rqIndex - 1].SubItems[1].Text;
                    }
                }

                //Re-select the rows including the new row below, but make the first row unselected
                foreach (int rqIndex in rqIndices)
                {
                    subRqListView.Items[rqIndex + 1].Selected = true;
                }
                subRqListView.Items[rqIndices[0]].Selected = false;
                subRqListView.Select();
            }

            //*** if sequential selection is not at the end of the view list
            if (BlCheckSequen(rqIndices) && intLastSelItemPos < intLastViewItemPos)
            {
                //** If the row below the selectio is empty, move down one row
                if (subRqListView.Items[intLastSelItemPos + 1].SubItems[0].Text == "")
                {
                    //go from the last selected row to the top 
                    for (int i = rqIndices.Count - 1; i >= 0; i--)
                    {
                        int rqIndex = rqIndices[i];
                        //make the row below the value of current row
                        subRqListView.Items[rqIndex + 1].SubItems[0].Text = subRqListView.Items[rqIndex].SubItems[0].Text;
                        subRqListView.Items[rqIndex + 1].SubItems[1].Text = subRqListView.Items[rqIndex].SubItems[1].Text;

                        //in case the current row is the first of the selection, make it empty
                        if (i == 0)
                        {
                            subRqListView.Items[rqIndex].SubItems[0].Text = "";
                            subRqListView.Items[rqIndex].SubItems[1].Text = "";
                        }
                    }

                    //Re-select the rows including the new row below, but make the first row unselected
                    foreach (int rqIndex in rqIndices)
                    {
                        subRqListView.Items[rqIndex + 1].Selected = true;
                    }
                    subRqListView.Items[rqIndices[0]].Selected = false;
                    subRqListView.Select();
                }

                //** If the row below the selections is not empty, a bit more complicated
                else
                {
                    int intBlank = 0;
                    int intPosBlank = 0;
                    bool blMovedToSpace = false;
                    // * Block of selections moved to the empty spaces that are before the end of the list
                    // * going through from the second row below the selection last row, up to the row above the last row in the list
                    for (int i = intLastSelItemPos + 2; i <= intLastViewItemPos - 1; i++)
                    {
                        //if the current row is a blank, and if this is the first blank or a consequtive blank
                        if (subRqListView.Items[i].SubItems[0].Text == "" && (i - intPosBlank == 1 || intPosBlank == 0))
                        {
                            //record ths position of this blank
                            intPosBlank = i;
                            //add to the consequtive blank total
                            intBlank++;
                            //if the blank number is already same as the selected total rows
                            if (intBlank == intSelTotal)
                            {
                                //Going through from first blank to the last blank, copy the values one by one, flag the selections have been moved
                                for (int j = intSelTotal - 1; j >= 0; j--)
                                {
                                    subRqListView.Items[i - j].SubItems[0].Text = subRqListView.Items[intLastSelItemPos - j].SubItems[0].Text;
                                    subRqListView.Items[i - j].SubItems[1].Text = subRqListView.Items[intLastSelItemPos - j].SubItems[1].Text;
                                    subRqListView.Items[intLastSelItemPos - j].SubItems[0].Text = "";
                                    subRqListView.Items[intLastSelItemPos - j].SubItems[1].Text = "";
                                    blMovedToSpace = true;
                                }
                                //Unselect the current selecion, select the blank rows (now with copied values)
                                foreach (int rqIndex in rqIndices)
                                {
                                    subRqListView.Items[rqIndex].Selected = false;
                                }
                                for (int j = intSelTotal - 1; j >= 0; j--)
                                {
                                    subRqListView.Items[i - j].Selected = true;
                                }
                                subRqListView.Select();
                                break;
                            }
                        }
                        else
                        //if not a blank reset the blank number and blank position
                        {
                            intPosBlank = 0;
                            intBlank = 0;
                        }
                    }

                    //* Block of selections Move to the end of the list, add extra rows
                    //* If there are enough spaces below the end of the view list, and the move to space did not happen before
                    if (intLastSheetItemPos - intLastViewItemPos >= intSelTotal && !blMovedToSpace)
                    {
                        //go through each selected row, copy texts and make a new row, then make the row empty
                        foreach (int rqIndex in rqIndices)
                        {
                            string[] strRows = new string[2];
                            strRows[0] = subRqListView.Items[rqIndex].SubItems[0].Text;
                            strRows[1] = subRqListView.Items[rqIndex].SubItems[1].Text;
                            ListViewItem rqRowitem = new ListViewItem(strRows);
                            subRqListView.Items.Add(rqRowitem);
                            subRqListView.Items[rqIndex].SubItems[0].Text = "";
                            subRqListView.Items[rqIndex].SubItems[1].Text = "";
                        }

                        //unselect the current selection, make the new rows selected
                        foreach (int rqIndex in rqIndices)
                        {
                            subRqListView.Items[rqIndex].Selected = false;
                        }
                        for (int i = 1; i <= intSelTotal; i++)
                        {
                            subRqListView.Items[intLastViewItemPos + i].Selected = true;
                        }
                        subRqListView.Select();
                    }
                }
            }
        }

        private void MoveDown()
        {
            if (rqTgtView.SelectedItems.Count == 1)
            {
                ListViewItem selected = rqTgtView.SelectedItems[0];
                int index = selected.Index;
                int total = rqTgtView.SelectedItems.Count;

                if (index == -1)
                {
                    rqTgtView.Items.Remove(selected);
                    rqTgtView.Items.Insert(0, selected);
                }
                else
                {
                    rqTgtView.Items.Remove(selected);
                    rqTgtView.Items.Insert(index + 1, selected);
                }
            }
            else
            {
                MessageBox.Show("Please select only one item.", "Item Selected", MessageBoxButtons.OK);
            }
        }

        private void MoveUp()
        {
            if (rqTgtView.SelectedItems.Count == 1)
            {
                ListViewItem selected = rqTgtView.SelectedItems[0];
                int index = selected.Index;
                int total = rqTgtView.SelectedItems.Count;

                if (index == 0)
                {
                    rqTgtView.Items.Remove(selected);
                    rqTgtView.Items.Insert(total - 1, selected);
                }
                else
                {
                    rqTgtView.Items.Remove(selected);
                    rqTgtView.Items.Insert(index - 1, selected);
                }
            }
            else
            {
                MessageBox.Show("Please select only one item.", "Item Selected", MessageBoxButtons.OK);
            }
        }

        //*** Function for move up rows in a listview
        private static void rqMoveUpRows(ListView subRqListView)
        {
            ListView.SelectedIndexCollection rqIndices = subRqListView.SelectedIndices;
            //make sure there is selection, to avoid index range out :intLastSelItemPos
            if (rqIndices.Count != 0)
            {
                int intSelTotal = rqIndices.Count;
                int intLastAbovePos = rqIndices[0] - 1;
                int intLastSelItemPos = rqIndices[intSelTotal - 1];
                int intLastViewItemPos = subRqListView.Items.Count - 1;

                //*** if a sequential selection, the row above is empty and current selection is not at the top of the row
                if (BlCheckSequen(rqIndices) && intLastAbovePos != -1 && subRqListView.Items[intLastAbovePos].SubItems[0].Text == "")
                {
                    //Get items from top to down in the selection
                    for (int i = 0; i <= intSelTotal - 1; i++)
                    {
                        //A item in the selection takes the value of the item below, starting with the row above the selection
                        subRqListView.Items[rqIndices[0] + i - 1].SubItems[0].Text = subRqListView.Items[rqIndices[0] + i].SubItems[0].Text;
                        subRqListView.Items[rqIndices[0] + i - 1].SubItems[1].Text = subRqListView.Items[rqIndices[0] + i].SubItems[1].Text;

                        //If it reaches the last selected row
                        if (intLastSelItemPos == rqIndices[0] + i)
                        {
                            //if the last selected row happens to be the last of list, then delete this item
                            if (intLastSelItemPos == intLastViewItemPos)
                            {
                                subRqListView.Items[rqIndices[0] + i].Remove();
                            }
                            //otherwise make this row blank
                            else
                            {
                                subRqListView.Items[rqIndices[0] + i].SubItems[0].Text = "";
                                subRqListView.Items[rqIndices[0] + i].SubItems[1].Text = "";
                            }
                        }
                    }

                    //select the row above, and re-select all the rows
                    subRqListView.Items[intLastAbovePos].Selected = true;
                    foreach (int rqIndex in rqIndices)
                    {
                        subRqListView.Items[rqIndex].Selected = true;
                    }

                    //if the last selected row is not the end of the list (which is deleted) then unselect the last row (which has been moved up)
                    if (intLastSelItemPos != intLastViewItemPos)
                    {
                        subRqListView.Items[intLastSelItemPos].Selected = false;
                    }
                    subRqListView.Select();
                }
                //*** If the row above is not empty
                else
                {
                    int intBlank = 0;
                    int intPosBlank = 0;

                    //check through from the second row above the selection, up to the top
                    for (int i = intLastAbovePos - 1; i >= 0; i--)
                    {
                        //** if this row is empty and it is a consequtive blank or first blank
                        if (subRqListView.Items[i].SubItems[0].Text == "" && (intPosBlank - i == 1 || intPosBlank == 0))
                        {
                            //blank number + 1, record this position
                            intPosBlank = i;
                            intBlank++;
                            //if the blank number reaches the total selection number
                            if (intBlank == intSelTotal)
                            {
                                //go through from the last empty row up to the top of the empty row,
                                for (int j = 0; j <= intSelTotal - 1; j++)
                                {
                                    // copy the value from the corresponding row
                                    subRqListView.Items[i + intSelTotal - 1 - j].SubItems[0].Text = subRqListView.Items[intLastSelItemPos - j].SubItems[0].Text;
                                    subRqListView.Items[i + intSelTotal - 1 - j].SubItems[1].Text = subRqListView.Items[intLastSelItemPos - j].SubItems[1].Text;
                                    // if the selection is to the bottom of the list
                                    if (intLastSelItemPos == intLastViewItemPos)
                                    {
                                        //then delete the last row
                                        subRqListView.Items[intLastSelItemPos - j].Remove();
                                    }
                                    // if the selection is not up to the bottom
                                    else
                                    {
                                        //make the selection row empty, and unselect the row
                                        subRqListView.Items[intLastSelItemPos - j].SubItems[0].Text = "";
                                        subRqListView.Items[intLastSelItemPos - j].SubItems[1].Text = "";
                                        subRqListView.Items[intLastSelItemPos - j].Selected = false;
                                    }
                                }
                                //select the empty rows 
                                for (int j = 0; j <= intSelTotal - 1; j++)
                                {
                                    subRqListView.Items[i + intSelTotal - 1 - j].Selected = true;
                                }
                                subRqListView.Select();
                                break;
                            }
                        }
                        //** otherwise reset the blank number to 0
                        else
                        {
                            intPosBlank = 0;
                            intBlank = 0;
                        }
                    }

                }
            }
        }

        //*** Check if selected listview items are sequential
        public static bool BlCheckSequen(ListView.SelectedIndexCollection subCol)
        {
            bool blIsSequen = true;
            if (subCol.Count > 1)
            {
                for (int i = 0; i < subCol.Count - 1; i++)
                {
                    if (subCol[i + 1] - subCol[i] != 1)
                    {
                        blIsSequen = false;
                        break;
                    }
                }
            }
            else if (subCol.Count == 0)
            {
                blIsSequen = false;
            }
            return blIsSequen;
        }

        private void rqTgtSheet_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            List<string[]> rqListSel = new List<string[]>();
            ListView.SelectedIndexCollection rqIndices = rqTgtSheet.SelectedIndices;
            int i = 0;
            foreach (int rqIndex in rqIndices)
            {
                string[] rqStrOneRow = new string[3];
                rqStrOneRow[0] = rqTgtSheet.Items[rqIndex - i].SubItems[0].Text;
                rqStrOneRow[1] = rqTgtSheet.Items[rqIndex - i].SubItems[1].Text;
                rqStrOneRow[2] = rqTgtSheet.Items[rqIndex - i].SubItems[2].Text;
                rqListSel.Add(rqStrOneRow);
                rqTgtSheet.Items[rqIndex - i].Remove();
                i++;
            }

            foreach (string[] strElem in rqListSel)
            {
                string[] strRows = new string[3];
                strRows[0] = strElem[0];
                strRows[1] = strElem[1];
                strRows[2] = strElem[2];
                ListViewItem rqRowsCanitem = new ListViewItem(strRows);
                sheetList.Items.Add(rqRowsCanitem);
            }
        }

        private void rqSheetRmv_Click(object sender, EventArgs e)
        {
            List<string[]> rqListSel = new List<string[]>();
            ListView.SelectedIndexCollection rqIndices = rqTgtSheet.SelectedIndices;
            int i = 0;
            foreach (int rqIndex in rqIndices)
            {
                string[] rqStrOneRow = new string[3];
                rqStrOneRow[0] = rqTgtSheet.Items[rqIndex - i].SubItems[0].Text;
                rqStrOneRow[1] = rqTgtSheet.Items[rqIndex - i].SubItems[1].Text;
                rqStrOneRow[2] = rqTgtSheet.Items[rqIndex - i].SubItems[2].Text;
                rqListSel.Add(rqStrOneRow);
                rqTgtSheet.Items[rqIndex - i].Remove();
                i++;
            }

            foreach (string[] strElem in rqListSel)
            {
                string[] strRows = new string[3];
                strRows[0] = strElem[0];
                strRows[1] = strElem[1];
                strRows[2] = strElem[2];
                ListViewItem rqRowsCanitem = new ListViewItem(strRows);
                sheetList.Items.Add(rqRowsCanitem);
            }
        }

        private void rqTgtView_DoubleClick(object sender, EventArgs e)
        {
            List<string[]> rqListSel = new List<string[]>();
            ListView.SelectedIndexCollection rqIndices = rqTgtView.SelectedIndices;
            int intSelFirst = rqIndices[0];
            int intSelTotal = rqIndices.Count;
            foreach (int rqIndex in rqIndices)
            {
                if (rqTgtView.Items[rqIndex].SubItems[0].Text != "")
                {
                    string[] rqStrOneRow = new string[2];
                    rqStrOneRow[0] = rqTgtView.Items[rqIndex].SubItems[0].Text;
                    rqStrOneRow[1] = rqTgtView.Items[rqIndex].SubItems[1].Text;
                    rqListSel.Add(rqStrOneRow);
                }
            }

            foreach (string[] strElem in rqListSel)
            {
                string[] strRows = new string[2];
                strRows[0] = strElem[0];
                strRows[1] = strElem[1];
                ListViewItem rqRowsCanitem = new ListViewItem(strRows);
                rqViewsList.Items.Add(rqRowsCanitem);
            }

            for (int j = 1; j <= intSelTotal; j++)
            {
                rqTgtView.Items[intSelFirst].Remove();
            }
        }

        private void rqViewRmv_Click(object sender, EventArgs e)
        {
            List<string[]> rqListSel = new List<string[]>();
            ListView.SelectedIndexCollection rqIndices = rqTgtView.SelectedIndices;
            int intSelFirst = rqIndices[0];
            int intSelTotal = rqIndices.Count;
            foreach (int rqIndex in rqIndices)
            {
                if (rqTgtView.Items[rqIndex].SubItems[0].Text != "")
                {
                    string[] rqStrOneRow = new string[2];
                    rqStrOneRow[0] = rqTgtView.Items[rqIndex].SubItems[0].Text;
                    rqStrOneRow[1] = rqTgtView.Items[rqIndex].SubItems[1].Text;
                    rqListSel.Add(rqStrOneRow);
                }
            }

            foreach (string[] strElem in rqListSel)
            {
                string[] strRows = new string[2];
                strRows[0] = strElem[0];
                strRows[1] = strElem[1];
                ListViewItem rqRowsCanitem = new ListViewItem(strRows);
                rqViewsList.Items.Add(rqRowsCanitem);
            }

            for (int j = 1; j <= intSelTotal; j++)
            {
                rqTgtView.Items[intSelFirst].Remove();
            }
        }

        private void rqTgtLegend_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            ListView.SelectedIndexCollection rqIndices = rqTgtLegend.SelectedIndices;
            int intSelFirst = rqIndices[0];
            int intSelTotal = rqIndices.Count;
            for (int j = 1; j <= intSelTotal; j++)
            {
                rqTgtLegend.Items[intSelFirst].Remove();
            }
        }

        private void rqLegendRmv_Click(object sender, EventArgs e)
        {
            ListView.SelectedIndexCollection rqIndices = rqTgtLegend.SelectedIndices;
            int intSelFirst = rqIndices[0];
            int intSelTotal = rqIndices.Count;
            for (int j = 1; j <= intSelTotal; j++)
            {
                rqTgtLegend.Items[intSelFirst].Remove();
            }
        }

        private void rqViewUp_Click(object sender, EventArgs e)
        {
            MoveUp();
            foreach (ListViewItem item in rqTgtView.Items)
            {
                item.BackColor = item.Index % 2 == 0 ? System.Drawing.Color.White : System.Drawing.Color.LightGray;
            }
        }

        private void rqViewDown_Click(object sender, EventArgs e)
        {
            MoveDown();
            foreach (ListViewItem item in rqTgtView.Items)
            {
                item.BackColor = item.Index % 2 == 0 ? System.Drawing.Color.White : System.Drawing.Color.LightGray;
            }
        }

        private void rqLegendDown_Click(object sender, EventArgs e)
        {
            rqMoveDownRows(rqTgtSheet, rqTgtLegend);
        }

        private void rqLegendUp_Click(object sender, EventArgs e)
        {
            rqMoveUpRows(rqTgtLegend);
        }

        private void splitContainer1_SplitterMoved(object sender, SplitterEventArgs e)
        {
            Properties.Settings.Default.Save();
        }

        private void splitContainer2_SplitterMoved(object sender, SplitterEventArgs e)
        {
            Properties.Settings.Default.Save();
        }

        private void btn_StartViewPlacer_Click(object sender, EventArgs e)
        {
            int fail = 0;
            int successfulView = 0;
            int successfulLegend = 0;

            using (var trans = new Transaction(_doc, BecaTransactionsNames.SheetView_PlaceViewsonSheets.GetHumanReadableString()))
            {
                trans.Start(BecaTransactionsNames.SheetView_PlaceViewsonSheets.GetHumanReadableString());
                //going through all sheets in the list
                for (int i = 0; i < rqTgtSheet.Items.Count; i++)
                {
                    //Get sheet id
                    string strSheetId = rqTgtSheet.Items[i].SubItems[2].Text;
                    int intSheetId = Convert.ToInt32(strSheetId);
                    ElementId rqSheetEid = new ElementId(intSheetId);

                    //if view item is within sheet range
                    if (i < rqTgtView.Items.Count)
                    {
                        //if view item is not empty
                        if (rqTgtView.Items[i].SubItems[1].Text != "")
                        {
                            //get view id and view object
                            var viewId = new ElementId(Convert.ToInt32(rqTgtView.Items[i].SubItems[1].Text));
                            var viewToPlace = _doc.GetElement(new ElementId(Convert.ToInt32(rqTgtView.Items[i].SubItems[1].Text))) as View;

                            //get placement point from textbox
                            var outline = viewToPlace.Outline;
                            XYZ placementPoint = new XYZ(BecaRevitUtilities.RevitUnitConvertor.MmToInternal(Convert.ToDouble(rqViewX.Text)), 
                                BecaRevitUtilities.RevitUnitConvertor.MmToInternal(Convert.ToDouble(rqViewY.Text)), 0);

                            //place the view
                            try
                            {
                                var schedules = _doc.GetElement(viewId) as View;
                                if (schedules.ViewType == Autodesk.Revit.DB.ViewType.Schedule)
                                {
                                    ScheduleSheetInstance.Create(_doc, rqSheetEid, viewId, placementPoint);
                                }
                                else if (schedules.ViewType == Autodesk.Revit.DB.ViewType.PanelSchedule)
                                {
                                    var view = _doc.GetElement(rqSheetEid) as View;
                                    PanelScheduleSheetInstance.Create(_doc, viewId, view);
                                }
                                else
                                {
                                    Viewport.Create(_doc, rqSheetEid, viewId, placementPoint);
                                }
                                successfulView++;
                            }
                            catch (Exception ex)
                            {

                                TaskDialog.Show("View creation error", ex.Message + "\n\n" + "Sheet name: " + _doc.GetElement(rqSheetEid).Name
                                    + "\nView name: " + _doc.GetElement(viewId).Name);
                                fail++;
                            }
                        }
                    }

                    //if the legend item is within sheet range
                    if (i < rqTgtLegend.Items.Count)
                    {
                        //if legend row is not empty
                        if (rqTgtLegend.Items[i].SubItems[1].Text != "")
                        {
                            string strLgdId = rqTgtLegend.Items[i].SubItems[1].Text;
                            int intLgdId = Convert.ToInt32(strLgdId);
                            ElementId rqLgdEid = new ElementId(intLgdId);
                            Element rqLgdElem = _doc.GetElement(rqLgdEid);
                            Autodesk.Revit.DB.View rqLgdToPlace = rqLgdElem as Autodesk.Revit.DB.View;

                            //get legend border, calc legend border width and height
                            BoundingBoxUV rqUVLgdPlace = rqLgdToPlace.Outline;
                            double rqWidth = rqUVLgdPlace.Max.U - rqUVLgdPlace.Min.U;
                            double rqHeight = rqUVLgdPlace.Max.V - rqUVLgdPlace.Min.V;

                            //get x,y value for the legend left top corder, divide mm number by 304.8
                            double dblLgdX = Convert.ToDouble(rqLgdX.Text) / 304.8 + rqWidth / 2;
                            double dblLgdY = Convert.ToDouble(rqLgdY.Text) / 304.8 - rqHeight / 2;
                            XYZ rqLgdPlace = new XYZ(dblLgdX, dblLgdY, 0);

                            //* Check if Legend is already on the sheet
                            //* this can happen as a candidate legends are not removed even they have been placed already
                            //Get sheet object and all the views hosted on it collection
                            Element rqSheetElem = _doc.GetElement(rqSheetEid);
                            ViewSheet rqSheetHost = rqSheetElem as ViewSheet;
                            ICollection<ElementId> rqExistViewsId = rqSheetHost.GetAllPlacedViews();

                            bool blPlaceLgd = true;
                            //go through all the views on the host sheet
                            foreach (ElementId Eid in rqExistViewsId)
                            {
                                //if this legend id Eid is the same as on of the views on the sheet
                                if (Eid == rqLgdEid)
                                {
                                    //flag the legend can not be placed on this sheet as the same legend is already there
                                    blPlaceLgd = false;
                                    break;
                                }
                            }
                            //Only place legend that hasn't been placed, otherwise no action
                            if (blPlaceLgd)
                            {
                                try
                                {
                                    Viewport.Create(_doc, rqSheetEid, rqLgdEid, rqLgdPlace);
                                    successfulLegend++;
                                }
                                catch (Exception ex)
                                {

                                    TaskDialog.Show("View creation error", ex.Message + "\n\n" + "Sheet name: " + _doc.GetElement(rqSheetEid).Name
                                    + "\nView name: " + _doc.GetElement(rqLgdEid).Name);
                                    fail++;
                                }

                            }
                        }
                    }
                }
                trans.Commit();
            }

            //Save the view origin location settings for next time use
            Properties.Settings.Default.strViewOriginX = rqViewX.Text;
            Properties.Settings.Default.strViewOriginY = rqViewY.Text;
            Properties.Settings.Default.strLgdOriginX = rqLgdX.Text;
            Properties.Settings.Default.strLgdOriginY = rqLgdY.Text;
            Properties.Settings.Default.Save();

            TaskDialog.Show("Beca MEP Tools", successfulView.ToString() + " Views and\n" + successfulLegend.ToString() + " Legends have been placed !" + "\n" + fail.ToString() + " failed.");
            if (successfulView > 0)
            {
                rqTgtSheet.Items.Clear();
                rqTgtView.Items.Clear();
                rqTgtLegend.Items.Clear();
            }
        }
        #endregion

        #region Advanced Mode Methods
        private void btn_RunAdvancedMode_Click(object sender, EventArgs e)
        {
            this.WindowState = FormWindowState.Minimized;
            if (VSC_AdvancedFormCoreLogic.RunLogic(_app, _doc))
            {
                this.DialogResult = DialogResult.OK;
            }
            else
            {
                this.WindowState = FormWindowState.Normal;
                this.BringToFront();
            }
        }

        private void btn_01_AssignDrawingType_Click(object sender, EventArgs e)
        {
            if (dgv_01_DrawingTypeList.SelectedRows[0].Cells[1].Value.ToString() == "")
            {
                MessageBox.Show("Please input Discipline Name");
                this.DialogResult = DialogResult.None;
            }
            else
            {
                foreach (DataGridViewCell item in adgv_01_AvailableVT.SelectedCells)
                {
                    if (item.ColumnIndex == 2)
                    {
                        if (dgv_01_DrawingTypeList.SelectedCells[3].Value.ToString() == "") // Format: BB.1.LIGHTING LAYOUT
                            item.Value =
                                dgv_01_DrawingTypeList.SelectedCells[1].Value.ToString() + "-" +
                                dgv_01_DrawingTypeList.SelectedCells[0].Value.ToString() + "-" +
                                dgv_01_DrawingTypeList.SelectedCells[2].Value.ToString();
                        else
                            item.Value =
                                dgv_01_DrawingTypeList.SelectedCells[1].Value.ToString() + "-" +
                                dgv_01_DrawingTypeList.SelectedCells[0].Value.ToString() + "-" +
                                dgv_01_DrawingTypeList.SelectedCells[3].Value.ToString();
                    }
                    if (item.ColumnIndex == 4) // DisciplineName Column
                        item.Value = dgv_01_DrawingTypeList.SelectedCells[4].Value.ToString();
                }
            }
        }

        private void SetInitialCustomNamingDictionary()
        {
            _customNamingParameters.Add(_keyOverallScale, "");
            _customNamingParameters.Add(_keyDependantScale, "");
            _customNamingParameters.Add(_keyLevelInputNumber, "");
            _customNamingParameters.Add(_keyLevelInputString, "");
            _customNamingParameters.Add(_keyDisciplineCode, "");
            _customNamingParameters.Add(_keyDisciplineNumber, "");
            _customNamingParameters.Add(_keyDrawingType, "");
            _customNamingParameters.Add(_keyScopeBoxInputNumber, "");
            _customNamingParameters.Add(_keyOverallScopeBoxInputString, ""); // 3 Scopebox Input Strings is getting from the same code
            _customNamingParameters.Add(_keyParentScopeBoxInputString, "");
            _customNamingParameters.Add(_keyDependantScopeBoxInputString, "");
        }

        private void SetInitialCustomNamingExampleDictionary()
        {
            _namingExamples.Add(_keyOverallScale, "1-200");
            _namingExamples.Add(_keyDependantScale, "1-100");
            _namingExamples.Add(_keyLevelInputNumber, "01");
            _namingExamples.Add(_keyLevelInputString, "LEVEL 1");
            _namingExamples.Add(_keyDisciplineCode, "BH");
            _namingExamples.Add(_keyDisciplineNumber, "2");
            _namingExamples.Add(_keyDrawingType, "CEILING LAYOUT");
            _namingExamples.Add(_keyScopeBoxInputNumber, "0");
            _namingExamples.Add(_keyOverallScopeBoxInputString, "OVERALL"); // 3 Scopebox Input Strings only for example
            _namingExamples.Add(_keyParentScopeBoxInputString, "PARENT");
            _namingExamples.Add(_keyDependantScopeBoxInputString, "DEPENDANT");
        }

        private void SetDefaultNamings()
        {
            _defaultSheetNumber = $"{_keyDisciplineCode}-{_keyDisciplineNumber}{_keyLevelInputNumber}{_keyScopeBoxInputNumber}";
            _defaultSheetName = $"{_keyLevelInputString} {_keyDrawingType} {_keyOverallScopeBoxInputString}";
            _defaultOverallViewName = $"{_keyLevelInputString} {_keyDrawingType} {_keyOverallScopeBoxInputString} {_keyOverallScale}";
            _defaultParentViewName = $"{_keyLevelInputString} {_keyDrawingType} {_keyParentScopeBoxInputString} {_keyDependantScale}";
            _defaultDependantViewName = $"{_keyLevelInputString} {_keyDrawingType} {_keyDependantScopeBoxInputString} {_keyDependantScale}";

            tb_SheetNumberInput.Text = _defaultSheetNumber;
            tb_SheetNameInput.Text = _defaultSheetName;
            tb_OverallViewNameInput.Text = _defaultOverallViewName;
            tb_ParentViewNameInput.Text = _defaultParentViewName;
            tb_DependentViewNameInput.Text = _defaultDependantViewName;

            lbl_SheetNumberExample.Text = InjectStringWithParameterValues(tb_SheetNumberInput.Text, _namingExamples);
            lbl_SheetNameExample.Text = InjectStringWithParameterValues(tb_SheetNameInput.Text, _namingExamples);
            lbl_OverallViewNameExample.Text = InjectStringWithParameterValues(tb_OverallViewNameInput.Text, _namingExamples);
            lbl_ParentViewNameExample.Text = InjectStringWithParameterValues(tb_ParentViewNameInput.Text, _namingExamples);
            lbl_DependentViewNameExample.Text = InjectStringWithParameterValues(tb_DependentViewNameInput.Text, _namingExamples);
        }


        /// <summary>
        /// This updates the advanced Main DataGridView whenever any values updated in the tabs.
        /// </summary>
        private void RefreshAdvancedDGV()
        {
            var dt = (DataTable)dgv_02_ViewsToBePlaced.DataSource;
            if (dt != null)
                dt.Clear();

            if (cb_01_OverallViewScale.SelectedItem == null || cb_01_DependantViewScale.SelectedItem == null)
                return;

            // Check if using custom naming
            var sheetNumberIsCustom = true;
            var sheetNameIsCustom = true;
            var overallViewNameIsCustom = true;
            var parentViewNameIsCustom = true;
            var dependantViewNameIsCustom = true;
            if (tb_SheetNumberInput.Text == _defaultSheetNumber) 
                sheetNumberIsCustom = false;
            if (tb_SheetNameInput.Text == _defaultSheetName) 
                sheetNameIsCustom = false;
            if (tb_OverallViewNameInput.Text == _defaultOverallViewName)
                overallViewNameIsCustom = false;
            if (tb_ParentViewNameInput.Text == _defaultParentViewName) 
                parentViewNameIsCustom = false;
            if (tb_DependentViewNameInput.Text == _defaultDependantViewName) 
                dependantViewNameIsCustom = false;

            // Checks inputs: Overall checked & selected? drawing type applied? 
            var overallCount = 0;
            var parentCount = 0;
            foreach (DataGridViewRow row in dgv_01_ScopeBox.Rows)
            {
                if (!(bool)row.Cells[0].Value)
                    continue;

                if (Convert.ToBoolean(row.Cells[4].Value))
                    overallCount++;
                else
                    parentCount++;
            }

            if (overallCount > 0 && parentCount == 0)
                CreateParent = false;
            else
                CreateParent = true;

            _overallScale = cb_01_OverallViewScale.SelectedItem.ToString();
            _dependantScale = cb_01_DependantViewScale.SelectedItem.ToString();

            // Update custom naming dictionary
            _customNamingParameters[_keyOverallScale] = _overallScale;
            _customNamingParameters[_keyDependantScale] = _dependantScale;

            foreach (DataGridViewRow rowLevel in dgv_01_Levels.Rows)
            {
                if (!(bool)rowLevel.Cells[0].Value)
                    continue;

                // This will get the selected Level
                var levelInputNumber = rowLevel.Cells[3].Value.ToString();
                var levelInputString = rowLevel.Cells[2].Value.ToString();
                foreach (DataGridViewRow rowView in adgv_01_AvailableVT.Rows)
                {
                    if (!(bool)rowView.Cells[0].Value)
                        continue;

                    // Get drawing type and sequence number
                    var drawingType = "";
                    var discipline = "";
                    var diciplineNumber = "";
                    if (rowView.Cells[3].Value != null && !string.IsNullOrEmpty(rowView.Cells[3].Value.ToString()))
                    {
                        discipline = rowView.Cells[3].Value?.ToString().Substring(0, 2);
                        drawingType = _selectedDrawingTypes.Find(x => x.Name == rowView.Cells[3].Value.ToString()).Name.Substring(5);
                        diciplineNumber = rowView.Cells[3].Value?.ToString()[3].ToString();
                    }

                    // Scope Box and Dependent View
                    foreach (DataGridViewRow rowScopeBox in dgv_01_ScopeBox.Rows)
                    {
                        if (!(bool)rowScopeBox.Cells[0].Value)
                            continue;

                        var scopeBoxInputString = rowScopeBox.Cells[2].Value?.ToString(); // 3 Scopebox Input Strings is getting from this code
                        var scopeBoxInputNumber = rowScopeBox.Cells[3].Value?.ToString();

                        // Update custom naming dictionary
                        _customNamingParameters[_keyLevelInputNumber] = levelInputNumber;
                        _customNamingParameters[_keyLevelInputString] = levelInputString;
                        _customNamingParameters[_keyDisciplineCode] = discipline;
                        _customNamingParameters[_keyDisciplineNumber] = diciplineNumber;
                        _customNamingParameters[_keyDrawingType] = drawingType;
                        _customNamingParameters[_keyOverallScopeBoxInputString] = scopeBoxInputString; // 3 Scopebox Input Strings is getting from the same code
                        _customNamingParameters[_keyParentScopeBoxInputString] = scopeBoxInputString;
                        _customNamingParameters[_keyDependantScopeBoxInputString] = scopeBoxInputString;
                        _customNamingParameters[_keyScopeBoxInputNumber] = scopeBoxInputNumber;
                        
                        // Get sheet number
                        var sheetNumber = string.Empty;
                        if (sheetNumberIsCustom)
                            sheetNumber = InjectStringWithParameterValues(tb_SheetNumberInput.Text, _customNamingParameters);
                        else
                            sheetNumber = VSC_Utilities.ExtractSheetNumberFromVT(discipline, diciplineNumber, levelInputNumber, scopeBoxInputNumber);
                        // Get sheet name
                        var sheetName = string.Empty;
                        if (sheetNameIsCustom)
                            sheetName = InjectStringWithParameterValues(tb_SheetNameInput.Text, _customNamingParameters);
                        else
                            sheetName = ExtractSheetNameFromVT(levelInputString, drawingType, scopeBoxInputString);

                        var designer = "";
                        var drawnby = "";
                        var verifiedby = "";
                        var date = "";

                        foreach (DataGridViewRow row in dgv_01_DrawingTypeList.Rows)
                        {
                            if (sheetNumber != "-" && row.Cells[1].Value.ToString() == sheetNumber[3].ToString() && row.Cells[2].Value.ToString() == sheetNumber.Substring(0, 2) && sheetName.Contains(row.Cells[3].Value.ToString()))
                            {
                                designer = row.Cells[6].Value.ToString();
                                drawnby = row.Cells[7].Value.ToString();
                                verifiedby = row.Cells[8].Value.ToString();
                                date = row.Cells[9].Value.ToString();
                            }
                        }

                        if (Convert.ToBoolean(rowScopeBox.Cells[4].Value.ToString()))
                        {
                            // Set OVERALL View name 
                            var overallViewName = string.Empty;
                            if (overallViewNameIsCustom)
                                overallViewName = InjectStringWithParameterValues(tb_OverallViewNameInput.Text, _customNamingParameters);
                            else
                                overallViewName = ExtractViewNameFromVT(levelInputString, drawingType, scopeBoxInputString, _overallScale);

                            // Check if the selected 2nd view template's view type is correct based on the primary view template
                            var secondViewTemplate = string.Empty;
                            var primaryViewTemplateViewType = _advancedData.ViewTemplates.Find(x => x.Name == rowView.Cells[2].Value.ToString())?.ViewType;
                            var vscSecondViewTemplate = _advancedData.ViewTemplates.Find(x => x.Name == rowLevel.Cells[4].Value?.ToString());
                            var secondViewTemplateViewType = vscSecondViewTemplate?.ViewType;
                            if (!string.IsNullOrEmpty(vscSecondViewTemplate?.Name) && secondViewTemplateViewType != primaryViewTemplateViewType)
                            {
                                MessageBox.Show($"The view type of the selected 2nd view template does not match the view type of the primary view template.\nPrimary view template's view type: '{primaryViewTemplateViewType}'");
                                rowLevel.Cells[4].Value = string.Empty;
                                continue;
                            }
                            else if (!string.IsNullOrEmpty(vscSecondViewTemplate?.Name) && secondViewTemplateViewType == primaryViewTemplateViewType)
                            {
                                secondViewTemplate = vscSecondViewTemplate.Name;
                                rowLevel.Cells[4].Value = secondViewTemplate;
                            }
                            // CHECK THIS WITH PARENT AND DEPENDANT VIEWS AS WELL !!!!!!

                            // Update OVERALL View row 
                            _advancedDataTable.Rows.Add(
                                        sheetNumber,
                                        sheetName,
                                        overallViewName,
                                        rowView.Cells[2].Value.ToString(), // Primary View Template
                                        secondViewTemplate, // 2nd View Template
                                        rowLevel.Cells[1].Value.ToString(), // Level Input String
                                        rowView.Cells[1].Value.ToString(), // View Type
                                        rowScopeBox.Cells[1].Value.ToString(), // Scopebox Input String
                                        _overallScale,
                                        rowView.Cells[5].Value.ToString(), // Cells[4] = DisciplineName
                                        OverallName, // Tag as Overall in Cell[11] = View 
                                        ExtractBaseViewNameFromVT(levelInputString, drawingType), // BaseViewName
                                        designer,
                                        drawnby,
                                        verifiedby,
                                        date
                                        );

                            // Set PARENT View name row in Tab 2 if checked
                            var parentViewName = string.Empty;
                            if (parentViewNameIsCustom)
                                parentViewName = InjectStringWithParameterValues(tb_ParentViewNameInput.Text, _customNamingParameters);
                            else
                                parentViewName = ExtractViewNameFromVT(levelInputString, drawingType, ParentName, _dependantScale);
                            // Update PARENT View row in Tab 2 if checked
                            if (CreateParent)
                                _advancedDataTable.Rows.Add(
                                "-", // No Sheet Number for Parent view
                                "-", // No Sheet Name for Parent view
                                parentViewName, 
                                rowView.Cells[2].Value.ToString(), // Primary View Template
                                rowLevel.Cells[4].Value?.ToString() ?? string.Empty,  // 2nd View Template
                                rowLevel.Cells[1].Value.ToString(), // Level Input String
                                rowView.Cells[1].Value.ToString(), // View Type
                                rowScopeBox.Cells[1].Value.ToString(), // Scopebox Input String
                                _dependantScale,
                                "", // No DisciplineName for parent not placed in sheet
                                ParentName, // Tag as Parent in Cell[11] = View
                                ExtractBaseViewNameFromVT(levelInputString, drawingType) // BaseViewName
                                );
                        }

                        // Set Dependent name
                        var dependentViewName = string.Empty;
                        if (dependantViewNameIsCustom)
                            dependentViewName = InjectStringWithParameterValues(tb_DependentViewNameInput.Text, _customNamingParameters);
                        else
                            dependentViewName = ExtractViewNameFromVT(levelInputString, drawingType, scopeBoxInputString, _dependantScale);
                        // Update Dependent
                        if (!Convert.ToBoolean(rowScopeBox.Cells[4].Value.ToString()) && CreateParent)
                        {
                            _advancedDataTable.Rows.Add(
                                sheetNumber,
                                sheetName,
                                dependentViewName,
                                rowView.Cells[2].Value.ToString(), // Primary View Template
                                rowLevel.Cells[4].Value?.ToString() ?? string.Empty, // 2nd View Template
                                rowLevel.Cells[1].Value.ToString(), // Level Input String
                                rowView.Cells[1].Value.ToString(), // View Type
                                rowScopeBox.Cells[1].Value.ToString(), // Scopebox Input String
                                _dependantScale,
                                rowView.Cells[5].Value.ToString(), // Cells[4] = DisciplineName
                                "Dependent", // Tag as Dependent in Cell[11] = View
                                ExtractBaseViewNameFromVT(levelInputString, drawingType), // BaseViewName
                                designer,
                                drawnby,
                                verifiedby,
                                date
                                );
                        }
                    }
                }
            }
            //// Save ScopeBox and Level inputs to XML
            //if (Directory.Exists(VSC_Path))
            //{
            //    DT_ScopeBox.WriteXml(XML_ScopeBoxFileName, XmlWriteMode.WriteSchema);
            //    DT_Level.WriteXml(XML_LevelFileName, XmlWriteMode.WriteSchema);
            //}
        }

        private string GetTitleBlockItem(DataGridView dgv, string sheetNumber, string sheetName, int index)
        {
            var str = "";
            foreach (DataGridViewRow row in dgv.Rows)
            {
                if (row.Cells[1].Value.ToString() == sheetNumber[3].ToString() && row.Cells[2].Value.ToString() == sheetNumber.Substring(0, 2) && sheetName.Contains(row.Cells[3].Value.ToString()))
                {
                    str = row.Cells[6].Value.ToString();
                }
            }
            return str;
        }

        private void btn_01_CreateViews_Click(object sender, EventArgs e)
        {
            // Check Beca Titleblock selection
            DialogResult dr = DialogResult.Yes;
            if (!lb_AdvTitleBlocks.SelectedItem.ToString().Contains("Beca"))
                dr = MessageBox.Show("Non Beca Titleblock is selected.\nWould you like to proceed?", "Titleblock Selection", MessageBoxButtons.YesNo);
            if (dr == DialogResult.No)
            {
                this.DialogResult = DialogResult.None;
                btn_ScopeBoxTabTickAll.SelectTab(4);
            }
            if (dr == DialogResult.Yes)
            {
                // Check duplicate sheet number in list
                var sheets = new List<string>();
                var sheetNumbers = new List<string>();
                var views = new List<string>();
                foreach (DataGridViewRow row in dgv_02_ViewsToBePlaced.Rows)
                {
                    if (row.Cells[(int)AdvancedItemsColumn.SheetName].Value.ToString() != "-")
                    {
                        sheets.Add(row.Cells[(int)AdvancedItemsColumn.SheetName].Value.ToString());
                        sheetNumbers.Add(row.Cells[(int)AdvancedItemsColumn.SheetNumber].Value.ToString());
                    }
                    views.Add(row.Cells[(int)AdvancedItemsColumn.ViewName].Value.ToString());
                }
                var duplicateSheetNumbers = sheetNumbers.GroupBy(x => x).Where(g => g.Count() > 1).Select(y => y.Key).ToList();
                var duplicateViewNames = views.GroupBy(x => x).Where(g => g.Count() > 1).Select(y => y.Key).ToList();

                // Check duplicate sheet number in project
                var duplicateSheetNumbersInProject = sheetNumbers.Intersect(_advancedData.SheetNumbers).ToList();
                var duplicateViewNamesInProject = views.Intersect(_advancedData.ViewNames).ToList();
                var sbSheets = new StringBuilder();
                var sbViews = new StringBuilder();
                if (duplicateSheetNumbersInProject.Count > 0 || duplicateViewNamesInProject.Count > 0)
                {
                    StringBuilder duplicatesMessage = new StringBuilder();
                    if (duplicateSheetNumbersInProject.Count > 0)
                    {
                        duplicatesMessage.AppendLine("Sheet Numbers already exist in the project. Please recheck the list:");
                        foreach (var d in duplicateSheetNumbersInProject)
                            duplicatesMessage.AppendLine(d);
                        duplicatesMessage.AppendLine();
                    }

                    if (duplicateViewNamesInProject.Count > 0)
                    {
                        duplicatesMessage.AppendLine("View Names already exist in the project. Please recheck the list:");
                        foreach (var d in duplicateViewNamesInProject)
                            duplicatesMessage.AppendLine(d);
                        duplicatesMessage.AppendLine();
                    }

                    if (duplicatesMessage.Length > 0)
                    {
                        MessageBox.Show(duplicatesMessage.ToString(), "Duplicate Items");
                        this.DialogResult = DialogResult.None;
                    }

                }
                else
                {
                    StringBuilder duplicatesMessage = new StringBuilder();
                    if (duplicateSheetNumbers.Any() || duplicateViewNames.Any())
                    {
                        if (duplicateSheetNumbers.Count > 0)
                        {
                            duplicatesMessage.AppendLine("Duplicate Sheet Numbers found. Please recheck list:");
                            foreach (var d in duplicateSheetNumbers)
                                duplicatesMessage.AppendLine(d);
                            duplicatesMessage.AppendLine();
                        }
                        if (duplicateViewNames.Count > 0)
                        {
                            duplicatesMessage.AppendLine("Duplicate View Names found. Please recheck list:");
                            foreach (var d in duplicateViewNames)
                                duplicatesMessage.AppendLine(d);
                            duplicatesMessage.AppendLine();
                        }
                        if (duplicatesMessage.Length > 0)
                        {
                            MessageBox.Show(duplicatesMessage.ToString(), "Duplicate Items");
                            this.DialogResult = DialogResult.None;
                        }

                    }
                    else // No duplicates > Go!
                    {
                        var titleblockId = _advancedData.TitleBlocks.Find(x => x.Name == lb_AdvTitleBlocks.SelectedItem.ToString()).Id;
                        foreach (DataGridViewRow row in dgv_02_ViewsToBePlaced.Rows)
                        {
                            Element scopeBox = null;
                            if (row.Cells[(int)AdvancedItemsColumn.ScopeBox].Value.ToString() != "")
                                scopeBox = _advancedData.ScopeBoxes.Find(x => x.ScopeBoxName == row.Cells[(int)AdvancedItemsColumn.ScopeBox].Value.ToString()).ScopeBox;

                            if (row.Cells[(int)AdvancedItemsColumn.View].Value.ToString() == OverallName || row.Cells[(int)AdvancedItemsColumn.View].Value.ToString() == ParentName)
                                ViewSheetCreationItems.Add(new VSC_2_CreationItems(
                                    _doc,
                                    false,
                                    row,
                                    titleblockId,
                                    _advancedData.Levels.Find(x => x.LevelName == row.Cells[(int)AdvancedItemsColumn.Level].Value.ToString()).Level,
                                    scopeBox
                                ));
                            else
                            {
                                ViewSheetCreationItems.Add(new VSC_2_CreationItems(
                                    _doc,
                                    true,
                                    row,
                                    titleblockId,
                                    _advancedData.Levels.Find(x => x.LevelName == row.Cells[(int)AdvancedItemsColumn.Level].Value.ToString()).Level,
                                    scopeBox
                                ));
                            }
                        }
                    }
                }

                TitleblockParameters = new VSC_TitleblockParameters();
                // Set Titleblock parameters
                TitleblockParameters.OriginalInColour = cb_OriginalInColour.Checked ? 1 : 0;
                TitleblockParameters.InterimIssue = cb_InterimIssue.Checked ? 1 : 0;
                TitleblockParameters.BecaCheckPrintVisibility = cb_BecaCheckPrintVisibility.Checked ? 1 : 0;


                List<RevitLinkType> unloadedLinks = null;
                // Temporarily unload all linked models
                using (Transaction trans = new Transaction(_doc, "Optimize View and Set Scope Box"))
                {
                    trans.Start();
                    unloadedLinks = VSC_Helper.UnloadLinkedModels(_doc);
                    trans.Commit();
                }

                // Run Logic
                if (AdvancedViewSheetLogic.RunLogic(_doc, ViewSheetCreationItems, _advancedData, TitleblockParameters))
                {
                    // Reload linked models
                    using (Transaction trans = new Transaction(_doc, "Reload Links"))
                    {
                        trans.Start();

                        VSC_Helper.ReloadLinkedModels(_doc, unloadedLinks);

                        trans.Commit();
                    }
                    this.DialogResult = DialogResult.OK;
                }
                else
                {
                    // Reload linked models
                    using (Transaction trans = new Transaction(_doc, "Reload Links"))
                    {
                        trans.Start();

                        VSC_Helper.ReloadLinkedModels(_doc, unloadedLinks);

                        trans.Commit();
                    }
                }
            }
        }

        /// <summary>
        /// This is used to trigger checked cell when value changes in Level list
        /// </summary>
        private void dgv_01_Levels_CellContentClick(object sender, DataGridViewCellEventArgs e)
        {
            dgv_01_Levels.CommitEdit(DataGridViewDataErrorContexts.Commit);
        }

        private void dgv_01_Levels_CellClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.ColumnIndex == 4)
            {
                using (var frmSecondary = new FrmSecondaryViewTemplate(_advancedData, adgv_01_AvailableVT[1, e.RowIndex].Value.ToString()))
                {
                    frmSecondary.ShowDialog();
                    if (frmSecondary.DialogResult == DialogResult.OK)
                    {
                        dgv_01_Levels[4, e.RowIndex].Value = frmSecondary.SelectedSecondViewTemplate.ToString();
                    }
                }
            }
        }

        private void dgv_01_Levels_CellValueChanged(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex == -1)
                return;

            if ((e.ColumnIndex == 0 || e.ColumnIndex == 2 || e.ColumnIndex == 3 || e.ColumnIndex == 4) && dgv_01_Levels.SelectedRows.Contains(dgv_01_Levels.Rows[e.RowIndex]))
            {
                RefreshAdvancedDGV();
            }
            //else if (e.ColumnIndex == 0)
            //{
            //    RefreshAdvancedDGV();
            //}
        }

        /// <summary>
        /// This is used to trigger checked cell when value changes in Drawing Type list
        /// </summary>
        private void dgv_01_DrawingTypeList_CellContentClick(object sender, DataGridViewCellEventArgs e)
        {
            dgv_01_DrawingTypeList.CommitEdit(DataGridViewDataErrorContexts.Commit);
        }

        private void dgv_01_DrawingTypeList_CellValueChanged(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex == -1)
                return;

            if (e.ColumnIndex == 0)
            {
                _selectedDrawingTypes = new List<VSC_DrawingType>();

                var dropdowncolumn = (DataGridViewComboBoxColumn)adgv_01_AvailableVT.Columns[3];
                dropdowncolumn.Items.Clear();
                foreach (DataGridViewRow row in dgv_01_DrawingTypeList.Rows)
                {
                    if (!(bool)row.Cells[0].Value)
                        continue;

                    var drawingType = "";
                    if (row.Cells[4].Value.ToString() == "") // Format: BB.1.LIGHTING LAYOUT
                        drawingType =
                            row.Cells[2].Value.ToString() + "-" +
                            row.Cells[1].Value.ToString() + "-" +
                            row.Cells[3].Value.ToString();
                    else
                        drawingType =
                            row.Cells[2].Value.ToString() + "-" +
                            row.Cells[1].Value.ToString() + "-" +
                            row.Cells[4].Value.ToString();

                    var vSC_DrawingType = new VSC_DrawingType()
                    {
                        Discipline = _advancedData.Disciplines.Find(x => x.Name == "" && x.DrawingType == "" && x.SequenceNumber == Convert.ToInt32("")),
                        Name = drawingType,
                        Designer = row.Cells[6].Value.ToString(),
                        DrawnBy = row.Cells[7].Value.ToString(),
                        VerifiedBy = row.Cells[8].Value.ToString(),
                        Date = row.Cells[9].Value.ToString()
                    };
                    _selectedDrawingTypes.Add(vSC_DrawingType);

                    dropdowncolumn.Items.Add(vSC_DrawingType.Name);
                }
            }
            else if (e.ColumnIndex == 6 || e.ColumnIndex == 7 || e.ColumnIndex == 8 || e.ColumnIndex == 9)
            {
                // Automatically add date
                if (e.ColumnIndex != 9)
                {
                    if (dgv_01_DrawingTypeList.Rows[e.RowIndex].Cells[6].Value.ToString() != "" || dgv_01_DrawingTypeList.Rows[e.RowIndex].Cells[7].Value.ToString() != "" || dgv_01_DrawingTypeList.Rows[e.RowIndex].Cells[8].Value.ToString() != "")
                        dgv_01_DrawingTypeList.Rows[e.RowIndex].Cells[9].Value = DateTime.Now.ToString("dd'.'MM'.'yyyy");
                }

                foreach (DataGridViewRow row in dgv_02_ViewsToBePlaced.Rows)
                {
                    try
                    {
                        if (row.Cells[(int)AdvancedItemsColumn.SheetNumber].Value.ToString().Length < 3)
                            continue;

                        if (dgv_01_DrawingTypeList[2, e.RowIndex].Value.ToString() == row.Cells[(int)AdvancedItemsColumn.SheetNumber].Value.ToString().Substring(0, 2) && // Discipline (BB) 
                        dgv_01_DrawingTypeList[1, e.RowIndex].Value.ToString() == row.Cells[(int)AdvancedItemsColumn.SheetNumber].Value.ToString()[3].ToString() && // SequenceNumber (2)
                        row.Cells[(int)AdvancedItemsColumn.SheetName].Value.ToString().Contains(dgv_01_DrawingTypeList[3, e.RowIndex].Value.ToString())) // DrawingType (FLOOR PLAN)
                        {
                            row.Cells[(int)AdvancedItemsColumn.Designer].Value = dgv_01_DrawingTypeList[6, e.RowIndex].Value.ToString(); // Designer
                            row.Cells[(int)AdvancedItemsColumn.DrawnBy].Value = dgv_01_DrawingTypeList[7, e.RowIndex].Value.ToString(); // Drawn by
                            row.Cells[(int)AdvancedItemsColumn.VerifiedBy].Value = dgv_01_DrawingTypeList[8, e.RowIndex].Value.ToString(); // Verified by
                            row.Cells[(int)AdvancedItemsColumn.Date].Value = dgv_01_DrawingTypeList[9, e.RowIndex].Value.ToString(); // Date

                        }
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show(ex.Message);
                    }

                }
            }
        }

        /// <summary>
        /// This is used to trigger checked cell when value changes in View list
        /// </summary>
        private void adgv_01_AvailableVT_CellContentClick(object sender, DataGridViewCellEventArgs e)
        {
            adgv_01_AvailableVT.CommitEdit(DataGridViewDataErrorContexts.Commit);
        }

        private void adgv_01_AvailableVT_CellValueChanged(object sender, DataGridViewCellEventArgs e)
        {
            if (e.ColumnIndex == 0 || e.ColumnIndex == 3 || e.ColumnIndex == 4)
            {
                RefreshAdvancedDGV();
            }
        }

        private void adgv_01_AvailableVT_CellClick(object sender, DataGridViewCellEventArgs e)
        {
            
        }

        private void adgv_01_AvailableVT_DataError(object sender, DataGridViewDataErrorEventArgs e)
        {
            if (e.Exception.Message == "DataGridViewComboBoxCell value is not valid.")
            {
                object value = adgv_01_AvailableVT[e.ColumnIndex, e.RowIndex].Value;
                if (!adgv_01_AvailableVT.Columns[e.ColumnIndex].Name.Contains(value.ToString()))
                {
                    adgv_01_AvailableVT.Columns[e.ColumnIndex].Name = value.ToString();
                    e.ThrowException = false;
                }
            }
        }

        /// <summary>
        /// This is used to trigger checked cell when value changes in Scope Box list
        /// </summary>
        private void dgv_01_ScopeBox_CellContentClick(object sender, DataGridViewCellEventArgs e)
        {
            dgv_01_ScopeBox.CommitEdit(DataGridViewDataErrorContexts.Commit);

        }

        private void dgv_01_ScopeBox_CellValueChanged(object sender, DataGridViewCellEventArgs e)
        {
            if (e.ColumnIndex == 0 || e.ColumnIndex == 2 || e.ColumnIndex == 3 || e.ColumnIndex == 4)
            {
                RefreshAdvancedDGV();
            }
        }

        private string ExtractSheetNameFromVT(string editedLevelName, string drawingType, string selectedScopeBox)
        {
            if (drawingType != null && !string.IsNullOrEmpty(drawingType))
                return editedLevelName + " " + drawingType + " " + selectedScopeBox;
            else
                return editedLevelName + " " + selectedScopeBox;
        }

        private string InjectStringWithParameterValues(string TextBoxText, Dictionary<string, string> parameterDictionary)
        {
            foreach (var kvp in parameterDictionary)
            {
                // Replace all occurrences of the placeholder key in the text with its value
                TextBoxText = TextBoxText.Replace(kvp.Key, kvp.Value);
            }
            return TextBoxText;
        }

        private string ExtractViewNameFromVT(string editedLevelName, string drawingType, string selectedScopeBox, string scale)
        {
            if (drawingType != null && !string.IsNullOrEmpty(drawingType))
                return editedLevelName + " " + drawingType + " " + selectedScopeBox + " " + scale;
            else
                return editedLevelName + " " + selectedScopeBox + " " + scale;
        }

        private string ExtractBaseViewNameFromVT(string editedLevelName, string drawingType)
        {
            if (drawingType != null && !string.IsNullOrEmpty(drawingType))
                return editedLevelName + " " + drawingType;
            else
                return editedLevelName;
        }

        private void tb_SearchDrawingType_TextChanged(object sender, EventArgs e)
        {
            SearchDataGridView(dgv_01_DrawingTypeList.Rows, 3, tb_SearchDrawingType);
        }
        private void tb_SearchViewTemplate_TextChanged(object sender, EventArgs e)
        {
            SearchDataGridView(adgv_01_AvailableVT.Rows, 2, tb_SearchViewTemplate);
        }

        private void SearchDataGridView(DataGridViewRowCollection rows, int cellIndex, System.Windows.Forms.TextBox textBox)
        {
            foreach (DataGridViewRow row in rows)
            {
                row.Visible = true;
            }
            foreach (DataGridViewRow row in rows)
            {
                if (!row.Cells[cellIndex].Value.ToString().ToLower().Contains(textBox.Text.ToLower()))
                {
                    row.Visible = false;
                }
            }
        }

        //private void tb_SearchViewTemplate_TextChanged_1(object sender, EventArgs e)
        //{
        //    _viewTemplateDT.DefaultView.RowFilter = string.Format("Name LIKE '%{0}%'", tb_SearchViewTemplate.Text);
        //}

        private void btn_SheetNumberParameterList_Click(object sender, EventArgs e)
        {
            using (var frmCustomNumberParameters = new FrmCustomNamingParameters(tb_SheetNumberInput, lbl_SheetNumberExample, _customNamingParameters))
            {
                frmCustomNumberParameters.ShowDialog();
            }
            RefreshAdvancedDGV();
        }

        private void btn_SheetNameParameterList_Click(object sender, EventArgs e)
        {
            using (var frmCustomNumberParameters = new FrmCustomNamingParameters(tb_SheetNameInput, lbl_SheetNameExample, _customNamingParameters))
            {
                frmCustomNumberParameters.ShowDialog();
            }
            RefreshAdvancedDGV();
        }

        private void btn_OverallViewNameParameterList_Click(object sender, EventArgs e)
        {
            using (var frmCustomNumberParameters = new FrmCustomNamingParameters(tb_OverallViewNameInput, lbl_OverallViewNameExample, _customNamingParameters))
            {
                frmCustomNumberParameters.ShowDialog();
            }
            RefreshAdvancedDGV();
        }

        private void btn_ParentViewNameParameterList_Click(object sender, EventArgs e)
        {
            using (var frmCustomNumberParameters = new FrmCustomNamingParameters(tb_ParentViewNameInput, lbl_ParentViewNameExample, _customNamingParameters))
            {
                frmCustomNumberParameters.ShowDialog();
            }
            RefreshAdvancedDGV();
        }

        private void btn_DependentViewNameParameterList_Click(object sender, EventArgs e)
        {
            using (var frmCustomNumberParameters = new FrmCustomNamingParameters(tb_DependentViewNameInput, lbl_DependentViewNameExample, _customNamingParameters))
            {
                frmCustomNumberParameters.ShowDialog();
            }
            RefreshAdvancedDGV();
        }

        private void btn_UpdateSheetNumberNaming_Click(object sender, EventArgs e)
        {
            lbl_SheetNumberExample.Text = InjectStringWithParameterValues(tb_SheetNumberInput.Text, _namingExamples);
            RefreshAdvancedDGV();
        }

        private void btn_UpdateSheetNameNaming_Click(object sender, EventArgs e)
        {
            lbl_SheetNameExample.Text = InjectStringWithParameterValues(tb_SheetNameInput.Text, _namingExamples);
            RefreshAdvancedDGV();
        }

        private void btn_UpdateOverallViewNameNaming_Click(object sender, EventArgs e)
        {
            lbl_OverallViewNameExample.Text = InjectStringWithParameterValues(tb_OverallViewNameInput.Text, _namingExamples);
            RefreshAdvancedDGV();
        }

        private void btn_UpdateParentViewNameNaming_Click(object sender, EventArgs e)
        {
            lbl_ParentViewNameExample.Text = InjectStringWithParameterValues(tb_ParentViewNameInput.Text, _namingExamples);
            RefreshAdvancedDGV();
        }

        private void btn_UpdateDependentViewNameNaming_Click(object sender, EventArgs e)
        {
            lbl_DependentViewNameExample.Text = InjectStringWithParameterValues(tb_DependentViewNameInput.Text, _namingExamples);
            RefreshAdvancedDGV();
        }

        private void btn_DefaultSheetNumberNaming_Click(object sender, EventArgs e)
        {
            tb_SheetNumberInput.Text = _defaultSheetNumber;
            lbl_SheetNumberExample.Text = InjectStringWithParameterValues(tb_SheetNumberInput.Text, _namingExamples);
            RefreshAdvancedDGV();
        }

        private void btn_DefaultSheetNameNaming_Click(object sender, EventArgs e)
        {
            tb_SheetNameInput.Text = _defaultSheetName;
            lbl_SheetNameExample.Text = InjectStringWithParameterValues(tb_SheetNameInput.Text, _namingExamples);
            RefreshAdvancedDGV();
        }

        private void btn_DefaultOverallViewNameNaming_Click(object sender, EventArgs e)
        {
            tb_OverallViewNameInput.Text = _defaultOverallViewName;
            lbl_OverallViewNameExample.Text = InjectStringWithParameterValues(tb_OverallViewNameInput.Text, _namingExamples);
            RefreshAdvancedDGV();
        }

        private void btn_DefaultParentViewNameNaming_Click(object sender, EventArgs e)
        {
            tb_ParentViewNameInput.Text = _defaultParentViewName;
            lbl_ParentViewNameExample.Text = InjectStringWithParameterValues(tb_ParentViewNameInput.Text, _namingExamples);
            RefreshAdvancedDGV();
        }

        private void btn_DefaultDependentViewNameNaming_Click(object sender, EventArgs e)
        {
            tb_DependentViewNameInput.Text = _defaultDependantViewName;
            lbl_DependentViewNameExample.Text = InjectStringWithParameterValues(tb_DependentViewNameInput.Text, _namingExamples);
            RefreshAdvancedDGV();
        }
        #endregion

        protected override void btnHelp_Click(object sender, EventArgs e)
        {
            using (var helpFrm = new FrmSheetCreatorHelp())
            {
                helpFrm.ShowDialog();
            }
        }

        private void chb_ShowAllViews_CheckedChanged(object sender, EventArgs e)
        {
            LoadDependentView();
        }

        private DataTable GetSelectedDrawingTypes()
        {
            var categories = new DataTable();
            categories.Columns.Add("DrawingType");
            categories.Rows.Add("");
            foreach (DataGridViewRow row in dgv_01_DrawingTypeList.Rows)
            {
                if (!(bool)row.Cells[0].Value)
                    continue;
                var drawingType = "";
                if (row.Cells[4].Value.ToString() == "") // Format: BB.1.LIGHTING LAYOUT
                    drawingType =
                        row.Cells[2].Value.ToString() + "-" +
                        row.Cells[1].Value.ToString() + "-" +
                        row.Cells[3].Value.ToString();
                else
                    drawingType =
                        row.Cells[2].Value.ToString() + "-" +
                        row.Cells[1].Value.ToString() + "-" +
                        row.Cells[4].Value.ToString();
                categories.Rows.Add(drawingType);
            }
            return categories;
        }

        private void btn_PreviewViewPlacement_Click(object sender, EventArgs e)
        {
            InitializeView();
        }

        private void InitializeView()
        {
            if (rqTgtView.SelectedItems.Count > 0)
            {
                var sheetId = new ElementId(Convert.ToInt32(rqTgtSheet.Items[rqTgtView.SelectedItems[0].Index].SubItems[2].Text));
                var titleBlockBoundingBox = new FilteredElementCollector(_doc, sheetId).OfCategory(BuiltInCategory.OST_TitleBlocks).FirstElement().get_BoundingBox(_doc.GetElement(sheetId) as ViewSheet);
                var centerLocation = titleBlockBoundingBox.Min.Add(titleBlockBoundingBox.Max).Multiply(0.5);
            
                ElementId viewId;

                using (Transaction tx = new Transaction(_doc, "Preview View Placement"))
                {
                    tx.Start();
                    ViewFamilyType vd = new FilteredElementCollector(_doc)
                                        .OfClass(typeof(ViewFamilyType))
                                        .Cast<ViewFamilyType>()
                                        .FirstOrDefault(q => q.ViewFamily == ViewFamily.FloorPlan);

                    //get view id and view object
                    var viewToDuplicate = _allViews.ToList().Find(v => v.Name == rqTgtView.SelectedItems[0].SubItems[0].Text);
                    viewId = viewToDuplicate.Duplicate(ViewDuplicateOption.Duplicate);


                    //get view border, calc view border width and height
                    var outline = viewToDuplicate.Outline;

                    //get x,y value for the view left top corder, divide mm number by 304.8
                    XYZ placementPoint;
                    if (cb_PlaceCenter.Checked)
                    {
                        placementPoint = new XYZ(centerLocation.X, centerLocation.Y, 0);
                        rqViewX.Text = Math.Round(BecaRevitUtilities.RevitUnitConvertor.InternalToMm(placementPoint.X),0).ToString();
                        rqViewY.Text = Math.Round(BecaRevitUtilities.RevitUnitConvertor.InternalToMm(placementPoint.Y),0).ToString();
                    }
                    else
                    {
                        placementPoint = new XYZ
                        (BecaRevitUtilities.RevitUnitConvertor.MmToInternal(Convert.ToDouble(rqViewX.Text)),
                        BecaRevitUtilities.RevitUnitConvertor.MmToInternal(Convert.ToDouble(rqViewY.Text)),
                        0);
                    }
                    
                    //place the view
                    try
                    {
                        var view = _doc.GetElement(viewId) as View;
                        if (view.ViewType == Autodesk.Revit.DB.ViewType.Schedule)
                        {
                            ScheduleSheetInstance.Create(_doc, sheetId, viewId, placementPoint);
                        }
                        else if (view.ViewType == Autodesk.Revit.DB.ViewType.PanelSchedule)
                        {
                            var panelSchedule = _doc.GetElement(sheetId) as View;
                            PanelScheduleSheetInstance.Create(_doc, viewId, panelSchedule);
                        }
                        else
                        {
                            Viewport.Create(_doc, sheetId, viewId, placementPoint);
                        }
                    }
                    catch (Exception ex)
                    {

                        TaskDialog.Show("View creation error", ex.Message + "\n\n" + "Sheet name: " + _doc.GetElement(sheetId).Name
                            + "\nView name: " + _doc.GetElement(viewId).Name);
                    }

                    tx.Commit();
                }

                using (var frmPlacementPreview = new FrmLocationPreview(_doc, sheetId))
                {
                    frmPlacementPreview.ShowDialog();
                    using (var trans = new Transaction(_doc, "Clear Preview"))
                    {
                        trans.Start();
                        _doc.Delete(viewId);
                        trans.Commit();
                    }
                }
            }
            else
            {
                MessageBox.Show("Select a view from the list to preview placement location");
                return;
            }

        }

        private void cb_PlaceCenter_CheckedChanged(object sender, EventArgs e)
        {
            if (cb_PlaceCenter.Checked)
            {
                rqViewX.Enabled = false;
                rqViewY.Enabled = false;
            }
            else
            {
                rqViewX.Enabled = true;
                rqViewY.Enabled = true;
            }
        }

        private void btn_ScopeBoxTabUnTickSelected_Click(object sender, EventArgs e)
        {
            foreach (DataGridViewRow row in dgv_01_ScopeBox.SelectedRows)
                CheckUnCheckSelectedRows(row, false);
        }

        private void btn_ScopeBoxTabTickSelected_Click(object sender, EventArgs e)
        {
            foreach (DataGridViewRow row in dgv_01_ScopeBox.SelectedRows)
                CheckUnCheckSelectedRows(row, true);
        }

        private void CheckUnCheckSelectedRows(DataGridViewRow row, bool check)
        {
            var chb = row.Cells[SelectedScopeBox.Index] as DataGridViewCheckBoxCell;
            if (check)
                chb.Value = true;
            else
                chb.Value = false;
        }

        
    }
}