﻿using Autodesk.Revit.Attributes;
using Autodesk.Revit.DB;
using Autodesk.Revit.UI;
using BecaCommand;
using BecaTransactionsNamesManager;
using Common.UI.Forms;
using MEP.SheetCreator.CoreLogic;
using MEP.SheetCreator.UI.Forms;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TaskDialog = Autodesk.Revit.UI.TaskDialog;
using View = Autodesk.Revit.DB.View;


namespace MEP.SheetCreator.RevitCommands
{
    [Transaction(TransactionMode.Manual)]

    class ViewSheetCreator : BecaBaseCommand
    {
        public override Result ExecuteBecaCommand(ExternalCommandData commandData, ref string message, Autodesk.Revit.DB.ElementSet elements)
        {
            UIApplication uiapp = commandData.Application;
            UIDocument uidoc = uiapp.ActiveUIDocument;
            var app = uiapp.Application;
            Document doc = uidoc.Document;


            _taskLogger.PreTaskStart();

            #region Core Logic
            var version = app.VersionNumber;
            var overallName = "OVERALL";
            var parentName = "PARENT";
            var scaleParameter = "View Scale";
            var secondaryVTParameter = "Beca_SecondaryViewTemplate";
            var data = new VSC_Data(doc);

            if (data.ScopeBoxes.Count() == 0)
            {
                TaskDialog.Show("Null Scope Box", "No Scope Box found.\nPlease create Overall and Zone Scope Boxes.");
                return Result.Failed;
            }
            if (data.ViewTemplates.Count() == 0)
            {
                TaskDialog.Show("Null View Template", "No View Template found.\nThis tool will not work without View Template.");
                return Result.Failed;
            }

            var frmSC = new FrmViewSheetCreator(doc, data, overallName, parentName, version);
            using (frmSC)
            {
                frmSC.ShowDialog();
            }
            #endregion

            // List views to place in sheet center
            List<VSC_CenterView> centerView = new List<VSC_CenterView>();

            if (frmSC.ViewSheetCreationItems.Count == 0)
                return Result.Failed;

            var overallOrParents = frmSC.ViewSheetCreationItems.Where(x => x.IsDependentView == false).ToList();
            var dependents = frmSC.ViewSheetCreationItems.Where(x => x.IsDependentView == true).ToList();

            int overallViewCount = 0;
            int parentViewCount = 0;
            int dependentViewCount = 0;
            int sheetCount = 0;

            // TRANSACTION 1: Create Overall/Parent Views (Lightning Fast!)
            using (var trans = new Transaction(doc, "Create Overall/Parent Views"))
            {
                trans.Start();

                string progressMessage = "{0} of " + overallOrParents.Count.ToString() + " views processed...";
                using (BecaProgressForm pf = new BecaProgressForm("Creating Overall/Parent Views", progressMessage, overallOrParents.Count))
                {
                    foreach (var item in overallOrParents)
                    {
                        // Primary and Secondary View Template set up
                        var primaryVT = data.ViewTemplates.Find(x => x.Name == item.ViewTemplateName).View;
                        View secondaryVT = null;
                        if (item.SecondaryViewTemplateName != "")
                        {
                            secondaryVT = data.ViewTemplates.Find(x => x.Name == item.SecondaryViewTemplateName).View;
                            VSC_Utilities.NotIncludeVTParameter(secondaryVT, scaleParameter, secondaryVTParameter);
                        }

                        // Create PARENT or OVERALL View
                        var view = VSC_Utilities.CreateView(doc, item.ViewFamilyType.Id, item.LevelId, item.ViewName);
                        // Set View Templates
                        view.get_Parameter(BuiltInParameter.VIEW_TEMPLATE).Set(primaryVT.Id);

                        // Set Secondary VT, Scope Box and Scale
                        if (secondaryVT != null)
                            view.ApplyViewTemplateParameters(secondaryVT);
                        if (view.LookupParameter(secondaryVTParameter) != null)
                            view.LookupParameter(secondaryVTParameter).Set(item.SecondaryViewTemplateName);
                        if (item.ScopeBox != null)
                            view.get_Parameter(BuiltInParameter.VIEWER_VOLUME_OF_INTEREST_CROP).Set(item.ScopeBox.Id);
                        if (item.Scale != null)
                            view.Scale = (int)item.Scale;

                        // Store the created view for later use
                        item.View = view;

                        if (item.OverallParentDependent == overallName)
                            overallViewCount++;

                        // Create PARENT View and assign ParentView property to ViewSheetCreationItems where IsDependent is true
                        if (item.OverallParentDependent == parentName)
                        {
                            frmSC.ViewSheetCreationItems.Where(x => x.OverallParentDependent == "Dependent" && x.BaseViewName == item.BaseViewName).ToList().ForEach(y => y.ParentView = view);
                            parentViewCount++;
                        }

                        pf.Increment();
                    }
                }
                trans.Commit();
            }

            // TRANSACTION 2: Create Dependent Views (Lightning Fast!)
            using (var trans = new Transaction(doc, "Create Dependent Views"))
            {
                trans.Start();

                string progressMessage = "{0} of " + dependents.Count.ToString() + " dependent views processed...";
                using (BecaProgressForm pf = new BecaProgressForm("Creating Dependent Views", progressMessage, dependents.Count))
                {
                    foreach (var item in dependents)
                    {
                        if (item.ParentView == null)
                            continue;

                        // Create dependent view
                        var dependentViewId = item.ParentView.Duplicate(ViewDuplicateOption.AsDependent);
                        var dependentView = doc.GetElement(dependentViewId) as ViewPlan;
                        dependentView.Name = item.DependentViewName;
                        if (item.ScopeBox != null)
                            dependentView.get_Parameter(BuiltInParameter.VIEWER_VOLUME_OF_INTEREST_CROP).Set(item.ScopeBox.Id);

                        // Store the created view for later use
                        item.View = dependentView;
                        dependentViewCount++;

                        pf.Increment();
                    }
                }
                trans.Commit();
            }

            // TRANSACTION 3: Create Sheets and Place Viewports (Lightning Fast!)
            var allItemsWithSheets = frmSC.ViewSheetCreationItems.Where(x => x.SheetNumber != "-").ToList();
            using (var trans = new Transaction(doc, "Create Sheets and Place Views"))
            {
                trans.Start();

                string progressMessage = "{0} of " + allItemsWithSheets.Count.ToString() + " sheets processed...";
                using (BecaProgressForm pf = new BecaProgressForm("Creating Sheets and Placing Views", progressMessage, allItemsWithSheets.Count))
                {
                    foreach (var item in allItemsWithSheets)
                    {
                        if (item.View == null) continue;

                        // Create Sheet
                        var sheet = VSC_Utilities.CreateSheet(doc, item.TitleBlockId, item.SheetName, item.SheetNumber);
                        // Set Sheet Parameters
                        VSC_Utilities.SetBecaSheetParameters(sheet, item);
                        // Place View
                        var titleblock = doc.GetElement(item.TitleBlockId);
                        var bb = titleblock.get_BoundingBox(sheet);
                        var titleblockCenter = bb.Min.Add(bb.Max).Multiply(0.5);
                        var vp = Viewport.Create(doc, sheet.Id, item.View.Id, new XYZ(0, 0, 0));
                        centerView.Add(new VSC_CenterView { View = vp, Sheet = sheet, Position = titleblockCenter });

                        sheetCount++;
                        pf.Increment();
                    }
                }
                trans.Commit();
            }

            TaskDialog.Show("View Sheet Creation", "Successfully created:\n" + overallViewCount + " Overall views\n" + parentViewCount + " Parent views\n" + dependentViewCount + " Dependent views\n" + sheetCount + " sheets");

            using (var trans = new Transaction(doc, "Center View in Sheets"))
            {
                trans.Start();
                foreach (var item in centerView)
                {
                    item.View.SetBoxCenter(item.Position);
                    VSC_Utilities.SetBecaTitleblockParameters(doc, item.Sheet);
                }
                trans.Commit();
            }

            _taskLogger.PostTaskEnd("Summary.");

            return Result.Succeeded;
        }

        public override string GetAddinAuthor()
        {
            return "Firza Utama";
        }

        public override string GetAddinName()
        {
            return AddinNames.ViewAndSheetCreator.Value;
        }

        public override string GetCommandSubName()
        {
            return "View Sheet Creator";
        }
    }
}
