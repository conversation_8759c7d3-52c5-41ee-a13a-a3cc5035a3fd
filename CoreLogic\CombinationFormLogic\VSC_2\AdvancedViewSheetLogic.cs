using Autodesk.Revit.DB;
using Autodesk.Revit.UI;
using Common.UI.Forms;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TaskDialog = Autodesk.Revit.UI.TaskDialog;
using View = Autodesk.Revit.DB.View;

namespace MEP.SheetCreator.CoreLogic.CombinationFormLogic.VSC_2
{
    public static class AdvancedViewSheetLogic
    {

        public static bool RunLogic(Document doc, List<VSC_2_CreationItems> viewSheetCreationItems, VSC_Data advancedData, VSC_TitleblockParameters titleblockParameters)
        {
            var overallName = "OVERALL";
            var parentName = "PARENT";
            var scaleParameter = "View Scale";
            var secondaryVTParameter = "Beca_SecondaryViewTemplate";

            try
            {
                // List views to place in sheet center
                List<VSC_CenterView> centerView = new List<VSC_CenterView>();

                if (viewSheetCreationItems.Count == 0)
                    return false;

                var overallOrParents = viewSheetCreationItems.Where(x => x.IsDependentView == false).ToList();
                var dependents = viewSheetCreationItems.Where(x => x.IsDependentView == true).ToList();

                int overallViewCount = 0;
                int parentViewCount = 0;
                int dependentViewCount = 0;
                int sheetCount = 0;

                // TRANSACTION 1: Create Overall/Parent Views (Lightning Fast!)
                using (var trans = new Transaction(doc, "Create Overall/Parent Views"))
                {
                    trans.Start();

                    string progressMessage = "{0} of " + overallOrParents.Count.ToString() + " views processed...";
                    using (BecaProgressForm pf = new BecaProgressForm("Creating Overall/Parent Views", progressMessage, overallOrParents.Count))
                    {
                        foreach (var item in overallOrParents)
                        {
                            // Primary and Secondary View Template set up
                            var primaryVT = advancedData.ViewTemplates.Find(x => x.Name == item.ViewTemplateName).View;
                            View secondaryVT = null;
                            if (item.SecondaryViewTemplateName != "")
                            {
                                secondaryVT = advancedData.ViewTemplates.Find(x => x.Name == item.SecondaryViewTemplateName).View;
                                VSC_Utilities.NotIncludeVTParameter(secondaryVT, scaleParameter, secondaryVTParameter);
                            }

                            // Create PARENT or OVERALL View
                            var view = VSC_Utilities.CreateView(doc, item.ViewFamilyType.Id, item.LevelId, item.ViewName);

                            // Set View Templates
                            view.get_Parameter(BuiltInParameter.VIEW_TEMPLATE).Set(primaryVT.Id);

                            // Set the scope box for the view
                            var id = item.ScopeBox.Id;
                            var currentScopeBoxId = view.get_Parameter(BuiltInParameter.VIEWER_VOLUME_OF_INTEREST_CROP).AsElementId();

                            // Set Secondary VT, Scope Box and Scale
                            if (secondaryVT != null)
                                view.ApplyViewTemplateParameters(secondaryVT);
                            if (view.LookupParameter(secondaryVTParameter) != null)
                                view.LookupParameter(secondaryVTParameter).Set(item.SecondaryViewTemplateName);
                            if (currentScopeBoxId == ElementId.InvalidElementId || !currentScopeBoxId.Equals(id))
                                view.get_Parameter(BuiltInParameter.VIEWER_VOLUME_OF_INTEREST_CROP).Set(item.ScopeBox.Id);
                            if (item.Scale != null)
                                view.Scale = (int)item.Scale;

                            // Store the created view for later use
                            item.View = view;

                            if (item.OverallParentDependent == overallName)
                                overallViewCount++;

                            // Create PARENT View and assign ParentView property to ViewSheetCreationItems where IsDependent is true
                            if (item.OverallParentDependent == parentName)
                            {
                                viewSheetCreationItems.Where(x => x.OverallParentDependent == "Dependent" && x.BaseViewName == item.BaseViewName).ToList().ForEach(y => y.ParentView = view);
                                parentViewCount++;
                            }

                            pf.Increment();
                        }
                    }
                    trans.Commit();
                }

                // TRANSACTION 2: Create Dependent Views (Lightning Fast!)
                using (var trans = new Transaction(doc, "Create Dependent Views"))
                {
                    trans.Start();

                    string progressMessage = "{0} of " + dependents.Count.ToString() + " dependent views processed...";
                    using (BecaProgressForm pf = new BecaProgressForm("Creating Dependent Views", progressMessage, dependents.Count))
                    {
                        foreach (var item in dependents)
                        {
                            if (item.ParentView == null)
                                continue;

                            // Create dependent view
                            var dependentViewId = item.ParentView.Duplicate(ViewDuplicateOption.AsDependent);
                            var dependentView = doc.GetElement(dependentViewId) as ViewPlan;
                            dependentView.Name = item.DependentViewName;
                            if (item.ScopeBox != null)
                                dependentView.get_Parameter(BuiltInParameter.VIEWER_VOLUME_OF_INTEREST_CROP).Set(item.ScopeBox.Id);

                            // Store the created view for later use
                            item.View = dependentView;
                            dependentViewCount++;

                            pf.Increment();
                        }
                    }
                    trans.Commit();
                }

                // TRANSACTION 3: Create Sheets and Place Viewports (Lightning Fast!)
                var allItemsWithSheets = viewSheetCreationItems.Where(x => x.SheetNumber != "-").ToList();
                using (var trans = new Transaction(doc, "Create Sheets and Place Views"))
                {
                    trans.Start();

                    string progressMessage = "{0} of " + allItemsWithSheets.Count.ToString() + " sheets processed...";
                    using (BecaProgressForm pf = new BecaProgressForm("Creating Sheets and Placing Views", progressMessage, allItemsWithSheets.Count))
                    {
                        foreach (var item in allItemsWithSheets)
                        {
                            if (item.View == null) continue;

                            // Create Sheet
                            var sheet = VSC_Utilities.CreateSheet(doc, item.TitleBlockId, item.SheetName, item.SheetNumber);
                            // Set Sheet Parameters
                            VSC_Utilities.VSC_2_SetBecaSheetParameters(sheet, item);

                            // Place View
                            var titleblock = doc.GetElement(item.TitleBlockId);
                            var bb = titleblock.get_BoundingBox(sheet);
                            var titleblockCenter = bb.Min.Add(bb.Max).Multiply(0.5);
                            var vp = Viewport.Create(doc, sheet.Id, item.View.Id, new XYZ(0, 0, 0));
                            centerView.Add(new VSC_CenterView { View = vp, Sheet = sheet, Position = titleblockCenter });

                            sheetCount++;
                            pf.Increment();
                        }
                    }
                    trans.Commit();
                }

                TaskDialog.Show("View Sheet Creation", "Successfully created:\n" + overallViewCount + " Overall views\n" + parentViewCount + " Parent views\n" + dependentViewCount + " Dependent views\n" + sheetCount + " sheets");

                using (var trans = new Transaction(doc, "Center View in Sheets"))
                {
                    trans.Start();
                    foreach (var item in centerView)
                    {
                        item.View.SetBoxCenter(item.Position);
                        VSC_Utilities.SetBecaTitleblockParameters(doc, item.Sheet);
                    }
                    trans.Commit();
                }

                using (var trans = new Transaction(doc, "Set titleblock parameters"))
                {
                    trans.Start();
                    foreach (var item in overallOrParents)
                    {
                        SetBecaTitleblockParameter(doc.GetElement(item.TitleBlockId) as FamilyInstance, titleblockParameters);
                    }
                    trans.Commit();
                }

                return true;
            }
            catch (Exception e)
            {
                TaskDialog.Show("Advanced VSC error", e.Message);
                return false;
            }
        }


        private static void SetBecaTitleblockParameter(FamilyInstance titleblock, VSC_TitleblockParameters titleblockParameters)
        {
            var sb = new StringBuilder();
            if (titleblock == null)
                return;

            foreach (Parameter param in titleblock.Parameters)
            {
                sb.AppendLine(param.Definition.Name);
                var definition = param.Definition;
                if (!param.IsReadOnly && definition.Name.ToLower().Contains("original in colour"))
                {
                    param.Set(titleblockParameters.OriginalInColour);
                }
                else if (!param.IsReadOnly && param.Definition.Name.ToLower().Contains("interim issue visibility"))
                {
                    param.Set(titleblockParameters.InterimIssue);
                }
                else if (!param.IsReadOnly && param.Definition.Name.ToLower().Contains("check print"))
                {
                    param.Set(titleblockParameters.BecaCheckPrintVisibility);
                }
            }
            TaskDialog.Show("Info", sb.ToString());

            //sheet.LookupParameter("Original in Colour")?.Set(titleblockParameters.OriginalInColour);
            //sheet.LookupParameter("Interim Issue")?.Set(titleblockParameters.InterimIssue);
            //sheet.LookupParameter("Beca Check Print Visibility")?.Set(titleblockParameters.BecaCheckPrintVisibility);
        }
    }
}
